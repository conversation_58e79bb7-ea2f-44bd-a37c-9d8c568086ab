{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./loading.service\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class SettingsService {\n  constructor(_http, _loadingService, _trans) {\n    this._http = _http;\n    this._loadingService = _loadingService;\n    this._trans = _trans;\n    this.customFieldsSubject = new Subject();\n    this.customFieldsValue = [];\n    this.initSettingsValue = {};\n    this.initSettingsSubject = new Subject();\n    this.systemSettingsSubject = new Subject();\n    this.defaultFields = [{\n      type: 'input',\n      key: 'first_name',\n      props: {\n        translate: true,\n        label: 'Surname',\n        required: true,\n        minLength: 1,\n        maxLength: 50\n      }\n    }, {\n      type: 'input',\n      key: 'last_name',\n      props: {\n        translate: true,\n        label: 'Other names',\n        required: true,\n        minLength: 1,\n        maxLength: 50\n      }\n    }, {\n      type: 'image-cropper',\n      key: 'photo',\n      props: {\n        translate: true,\n        label: 'Player photo',\n        required: true,\n        // upload_url: `${environment.apiUrl}/files/editor`,\n        upload_url: `${environment.apiUrl}/s3`,\n        dir: 'players',\n        accept: 'image/png,image/jpg,image/jpeg',\n        maxFileSize: 12000,\n        useCropperDialog: true,\n        config: {\n          width: 600,\n          height: 800,\n          resizableArea: false,\n          output: {\n            width: 600,\n            height: 800\n          }\n        },\n        test_image: 'assets/images/example_uploads/avatar.jpg'\n      }\n    }, {\n      type: 'input',\n      key: 'dob',\n      props: {\n        translate: true,\n        label: this._trans.instant('DOB'),\n        required: true,\n        type: 'date'\n      }\n    }, {\n      type: 'radio',\n      key: 'gender',\n      props: {\n        translate: true,\n        label: 'Gender',\n        required: true,\n        options: [{\n          label: 'Male',\n          value: 'Male'\n        }, {\n          label: 'Female',\n          value: 'Female'\n        }]\n      },\n      defaultValue: 'Male'\n    }, {\n      type: 'radio',\n      key: 'document_type',\n      props: {\n        translate: true,\n        label: 'Document type',\n        required: true,\n        type: 'select',\n        options: [{\n          value: 'HKID',\n          label: 'HKID'\n        }, {\n          value: 'Passport',\n          label: 'Passport'\n        }]\n      },\n      defaultValue: 'HKID'\n    }, {\n      type: 'image-cropper',\n      key: 'document_photo',\n      props: {\n        translate: true,\n        label: 'Document photo',\n        required: true,\n        upload_url: `${environment.apiUrl}/s3`,\n        dir: 'player_documents',\n        accept: 'image/png,image/jpg,image/jpeg',\n        maxFileSize: 12000,\n        useCropperDialog: true,\n        config: {\n          width: 900,\n          height: 600,\n          resizableArea: false,\n          output: {\n            width: 900,\n            height: 600\n          }\n        },\n        test_image: 'assets/images/example_uploads/hkid.jpg'\n      }\n    }, {\n      type: 'custom-date',\n      key: 'document_expiry_date',\n      props: {\n        translate: true,\n        label: 'Document expiry date',\n        required: false,\n        placeholder: 'dd/mm/yyyy'\n      },\n      expressions: {\n        'props.required': \"model.document_type === 'Passport'\",\n        hide: \"model.document_type === 'HKID'\"\n      }\n    }, {\n      type: 'ng-select',\n      key: 'club_id',\n      props: {\n        translate: true,\n        label: 'Club',\n        required: true,\n        closeOnSelect: true,\n        options: [],\n        placeholder: 'Select Club'\n      }\n    }];\n    this.customFieldsSubject.subscribe(res => {\n      this.customFieldsValue = res;\n    });\n    this.initSettingsSubject.subscribe(res => {\n      this.initSettingsValue = res;\n      console.log('initSettingsValue: ', this.initSettingsValue);\n    });\n  }\n  getCustomFields() {\n    let custom_fields = localStorage.getItem('custom_fields');\n    if (custom_fields) {\n      this.customFieldsSubject.next(JSON.parse(custom_fields));\n    } else {\n      this.getInitSettings();\n    }\n  }\n  get customFields() {\n    return this.customFieldsSubject.asObservable();\n  }\n  get initSettings() {\n    return this.initSettingsSubject.asObservable();\n  }\n  getSettingsData() {\n    return this.systemSettingsSubject.asObservable();\n  }\n  refreshSettings() {\n    this.getSettings();\n  }\n  getInitSettingFromLocalStorage() {\n    let initSettings = localStorage.getItem('initSettings');\n    if (initSettings) {\n      return JSON.parse(initSettings);\n    } else {\n      return null;\n    }\n  }\n  getInitSettings(from_storage = true) {\n    if (from_storage) {\n      let initSettings = localStorage.getItem('initSettings');\n      if (initSettings) {\n        initSettings = JSON.parse(initSettings);\n        this.initSettingsSubject.next(initSettings);\n        return this.initSettingsSubject;\n      } else {\n        from_storage = false;\n      }\n    }\n    if (!from_storage) {\n      return this._http.get(`${environment.apiUrl}/settings/init-json`).subscribe(res => {\n        let fields = res.custom_fields_default ? res.custom_fields_default : this.defaultFields;\n        if (res.hasOwnProperty('custom_fields') && res.custom_fields) {\n          res.custom_fields.forEach(element => {\n            if (element.type == 'image-cropper') {\n              element.props.upload_url = `${environment.apiUrl}/s3`;\n            }\n          });\n          // concat default fields with custom fields\n          fields = fields.concat(res.custom_fields);\n        }\n        // console.log('fields: ',fields);\n        // replace field with upload_url\n        fields.forEach(element => {\n          if (element.type == 'image-cropper') {\n            element.props.upload_url = `${environment.apiUrl}/s3`;\n          }\n        });\n        localStorage.setItem('custom_fields', JSON.stringify(fields));\n        localStorage.setItem('initSettings', JSON.stringify(res));\n        this.customFieldsSubject.next(fields);\n        this.initSettingsSubject.next(res);\n      }, err => {\n        console.log(err);\n      });\n    }\n  }\n  getSettings() {\n    this._loadingService.show();\n    console.log('getSettings');\n    return this._http.get(`${environment.apiUrl}/settings`).subscribe(res => {\n      this.systemSettingsSubject.next(res);\n    }, err => {\n      console.log(err);\n    });\n  }\n  updateSettings(key, data) {\n    let form_data = new FormData();\n    form_data.append('key', key);\n    form_data.append('value', JSON.stringify(data));\n    return this._http.post(`${environment.apiUrl}/settings`, form_data).pipe(map(res => {\n      this.systemSettingsSubject.next(res);\n      return res;\n    }));\n  }\n  getRequiredVersion() {\n    return this._http.get(`${environment.apiUrl}/settings/required-version`).pipe(map(res => {\n      return res;\n    }));\n  }\n  getNotificationSettings() {\n    return this._http.get(`${environment.apiUrl}/settings/notifications`).pipe(map(res => {\n      return res;\n    }));\n  }\n  getSettingNoAuth() {\n    return this._http.get(`${environment.apiUrl}/settings-no-auth`).pipe(map(res => {\n      return res;\n    }));\n  }\n  postAssignPolicy(user_id) {\n    const params = {\n      user_id: user_id\n    };\n    return this._http.post(`${environment.apiUrl}/settings-no-auth/accept-policy`, params).pipe(map(res => {\n      return res;\n    }));\n  }\n  getMetadaSettings() {\n    return this._http.get(`${environment.apiUrl}/settings/metadata`).pipe(map(res => {\n      return res;\n    }));\n  }\n  static #_ = this.ɵfac = function SettingsService_Factory(t) {\n    return new (t || SettingsService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.LoadingService), i0.ɵɵinject(i3.TranslateService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SettingsService,\n    factory: SettingsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,GAAG,QAAQ,gBAAgB;;;;;AAOpC,OAAM,MAAOC,eAAe;EA4I1BC,YACSC,KAAiB,EACjBC,eAA+B,EAC/BC,MAAwB;IAFxB,UAAK,GAALF,KAAK;IACL,oBAAe,GAAfC,eAAe;IACf,WAAM,GAANC,MAAM;IA9If,wBAAmB,GAAiB,IAAIN,OAAO,EAAO;IACtD,sBAAiB,GAAU,EAAE;IAC7B,sBAAiB,GAAQ,EAAE;IAC3B,wBAAmB,GAAiB,IAAIA,OAAO,EAAO;IACtD,0BAAqB,GAAiB,IAAIA,OAAO,EAAO;IAExD,kBAAa,GAAQ,CACnB;MACEO,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;;KAEd,EACD;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,aAAa;QACpBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;;KAEd,EACD;MACEP,IAAI,EAAE,eAAe;MACrBC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,cAAc;QACrBC,QAAQ,EAAE,IAAI;QACd;QACAG,UAAU,EAAE,GAAGhB,WAAW,CAACiB,MAAM,KAAK;QACtCC,GAAG,EAAE,SAAS;QACdC,MAAM,EAAE,gCAAgC;QACxCC,WAAW,EAAE,KAAK;QAClBC,gBAAgB,EAAE,IAAI;QACtBC,MAAM,EAAE;UACNC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,aAAa,EAAE,KAAK;UACpBC,MAAM,EAAE;YAAEH,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAG;SAClC;QACDG,UAAU,EAAE;;KAEf,EACD;MACEnB,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,IAAI,CAACL,MAAM,CAACqB,OAAO,CAAC,KAAK,CAAC;QACjCf,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;;KAET,EACD;MACEA,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE,IAAI;QACdgB,OAAO,EAAE,CACP;UAAEjB,KAAK,EAAE,MAAM;UAAEkB,KAAK,EAAE;QAAM,CAAE,EAChC;UAAElB,KAAK,EAAE,QAAQ;UAAEkB,KAAK,EAAE;QAAQ,CAAE;OAEvC;MACDC,YAAY,EAAE;KACf,EACD;MACEvB,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdqB,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAElB,KAAK,EAAE;QAAM,CAAE,EAChC;UAAEkB,KAAK,EAAE,UAAU;UAAElB,KAAK,EAAE;QAAU,CAAE;OAE3C;MACDmB,YAAY,EAAE;KACf,EACD;MACEvB,IAAI,EAAE,eAAe;MACrBC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,gBAAgB;QACvBC,QAAQ,EAAE,IAAI;QACdG,UAAU,EAAE,GAAGhB,WAAW,CAACiB,MAAM,KAAK;QACtCC,GAAG,EAAE,kBAAkB;QACvBC,MAAM,EAAE,gCAAgC;QACxCC,WAAW,EAAE,KAAK;QAClBC,gBAAgB,EAAE,IAAI;QACtBC,MAAM,EAAE;UACNC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,aAAa,EAAE,KAAK;UACpBC,MAAM,EAAE;YAAEH,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAG;SAClC;QACDG,UAAU,EAAE;;KAEf,EACD;MACEnB,IAAI,EAAE,aAAa;MACnBC,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,sBAAsB;QAC7BC,QAAQ,EAAE,KAAK;QACfmB,WAAW,EAAE;OACd;MACDC,WAAW,EAAE;QACX,gBAAgB,EAAE,oCAAoC;QACtDC,IAAI,EAAE;;KAET,EACD;MACE1B,IAAI,EAAE,WAAW;MACjBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdsB,aAAa,EAAE,IAAI;QACnBN,OAAO,EAAE,EAAE;QACXG,WAAW,EAAE;;KAEhB,CACF;IAOC,IAAI,CAACI,mBAAmB,CAACC,SAAS,CAAEC,GAAG,IAAI;MACzC,IAAI,CAACC,iBAAiB,GAAGD,GAAG;IAC9B,CAAC,CAAC;IACF,IAAI,CAACE,mBAAmB,CAACH,SAAS,CAAEC,GAAG,IAAI;MACzC,IAAI,CAACG,iBAAiB,GAAGH,GAAG;MAC5BI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACF,iBAAiB,CAAC;IAC5D,CAAC,CAAC;EACJ;EAEAG,eAAe;IACb,IAAIC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACzD,IAAIF,aAAa,EAAE;MACjB,IAAI,CAACT,mBAAmB,CAACY,IAAI,CAACC,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC,CAAC;KACzD,MAAM;MACL,IAAI,CAACM,eAAe,EAAE;;EAE1B;EAEA,IAAIC,YAAY;IACd,OAAO,IAAI,CAAChB,mBAAmB,CAACiB,YAAY,EAAE;EAChD;EAEA,IAAIC,YAAY;IACd,OAAO,IAAI,CAACd,mBAAmB,CAACa,YAAY,EAAE;EAChD;EAEAE,eAAe;IACb,OAAO,IAAI,CAACC,qBAAqB,CAACH,YAAY,EAAE;EAClD;EAEAI,eAAe;IACb,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,8BAA8B;IAC5B,IAAIL,YAAY,GAAGR,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,IAAIO,YAAY,EAAE;MAChB,OAAOL,IAAI,CAACC,KAAK,CAACI,YAAY,CAAC;KAChC,MAAM;MACL,OAAO,IAAI;;EAEf;EAEAH,eAAe,CAACS,YAAY,GAAG,IAAI;IACjC,IAAIA,YAAY,EAAE;MAChB,IAAIN,YAAY,GAAGR,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACvD,IAAIO,YAAY,EAAE;QAChBA,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACI,YAAY,CAAC;QACvC,IAAI,CAACd,mBAAmB,CAACQ,IAAI,CAACM,YAAY,CAAC;QAC3C,OAAO,IAAI,CAACd,mBAAmB;OAChC,MAAM;QACLoB,YAAY,GAAG,KAAK;;;IAIxB,IAAI,CAACA,YAAY,EAAE;MACjB,OAAO,IAAI,CAACvD,KAAK,CACdwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,qBAAqB,CAAC,CAC/CoB,SAAS,CACPC,GAAQ,IAAI;QACX,IAAIwB,MAAM,GAAGxB,GAAG,CAACyB,qBAAqB,GAClCzB,GAAG,CAACyB,qBAAqB,GACzB,IAAI,CAACC,aAAa;QACtB,IAAI1B,GAAG,CAAC2B,cAAc,CAAC,eAAe,CAAC,IAAI3B,GAAG,CAACO,aAAa,EAAE;UAC5DP,GAAG,CAACO,aAAa,CAACqB,OAAO,CAAEC,OAAO,IAAI;YACpC,IAAIA,OAAO,CAAC3D,IAAI,IAAI,eAAe,EAAE;cACnC2D,OAAO,CAACzD,KAAK,CAACM,UAAU,GAAG,GAAGhB,WAAW,CAACiB,MAAM,KAAK;;UAEzD,CAAC,CAAC;UACF;UACA6C,MAAM,GAAGA,MAAM,CAACM,MAAM,CAAC9B,GAAG,CAACO,aAAa,CAAC;;QAE3C;QAEA;QACAiB,MAAM,CAACI,OAAO,CAAEC,OAAO,IAAI;UACzB,IAAIA,OAAO,CAAC3D,IAAI,IAAI,eAAe,EAAE;YACnC2D,OAAO,CAACzD,KAAK,CAACM,UAAU,GAAG,GAAGhB,WAAW,CAACiB,MAAM,KAAK;;QAEzD,CAAC,CAAC;QACF6B,YAAY,CAACuB,OAAO,CAAC,eAAe,EAAEpB,IAAI,CAACqB,SAAS,CAACR,MAAM,CAAC,CAAC;QAC7DhB,YAAY,CAACuB,OAAO,CAAC,cAAc,EAAEpB,IAAI,CAACqB,SAAS,CAAChC,GAAG,CAAC,CAAC;QACzD,IAAI,CAACF,mBAAmB,CAACY,IAAI,CAACc,MAAM,CAAC;QACrC,IAAI,CAACtB,mBAAmB,CAACQ,IAAI,CAACV,GAAG,CAAC;MACpC,CAAC,EACAiC,GAAG,IAAI;QACN7B,OAAO,CAACC,GAAG,CAAC4B,GAAG,CAAC;MAClB,CAAC,CACF;;EAEP;EAEAb,WAAW;IACT,IAAI,CAACpD,eAAe,CAACkE,IAAI,EAAE;IAC3B9B,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,OAAO,IAAI,CAACtC,KAAK,CAACwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,WAAW,CAAC,CAACoB,SAAS,CAC9DC,GAAQ,IAAI;MACX,IAAI,CAACkB,qBAAqB,CAACR,IAAI,CAACV,GAAG,CAAC;IACtC,CAAC,EACAiC,GAAG,IAAI;MACN7B,OAAO,CAACC,GAAG,CAAC4B,GAAG,CAAC;IAClB,CAAC,CACF;EACH;EAEAE,cAAc,CAAChE,GAAG,EAAEiE,IAAS;IAC3B,IAAIC,SAAS,GAAG,IAAIC,QAAQ,EAAE;IAC9BD,SAAS,CAACE,MAAM,CAAC,KAAK,EAAEpE,GAAG,CAAC;IAC5BkE,SAAS,CAACE,MAAM,CAAC,OAAO,EAAE5B,IAAI,CAACqB,SAAS,CAACI,IAAI,CAAC,CAAC;IAE/C,OAAO,IAAI,CAACrE,KAAK,CAACyE,IAAI,CAAC,GAAG9E,WAAW,CAACiB,MAAM,WAAW,EAAE0D,SAAS,CAAC,CAACI,IAAI,CACtE7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,IAAI,CAACkB,qBAAqB,CAACR,IAAI,CAACV,GAAG,CAAC;MACpC,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEA0C,kBAAkB;IAChB,OAAO,IAAI,CAAC3E,KAAK,CACdwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,4BAA4B,CAAC,CACtD8D,IAAI,CACH7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA2C,uBAAuB;IACrB,OAAO,IAAI,CAAC5E,KAAK,CAACwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,yBAAyB,CAAC,CAAC8D,IAAI,CACxE7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEA4C,gBAAgB;IACd,OAAO,IAAI,CAAC7E,KAAK,CAACwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,mBAAmB,CAAC,CAAC8D,IAAI,CAClE7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEA6C,gBAAgB,CAACC,OAAO;IACtB,MAAMC,MAAM,GAAG;MAAED,OAAO,EAAEA;IAAO,CAAE;IACnC,OAAO,IAAI,CAAC/E,KAAK,CACdyE,IAAI,CAAC,GAAG9E,WAAW,CAACiB,MAAM,iCAAiC,EAAEoE,MAAM,CAAC,CACpEN,IAAI,CACH7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEAgD,iBAAiB;IACf,OAAO,IAAI,CAACjF,KAAK,CAACwD,GAAG,CAAC,GAAG7D,WAAW,CAACiB,MAAM,oBAAoB,CAAC,CAAC8D,IAAI,CACnE7E,GAAG,CAAEoC,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAAC;qBAlTUnC,eAAe;EAAA;EAAA;WAAfA,eAAe;IAAAoF,SAAfpF,eAAe;IAAAqF,YAFd;EAAM", "names": ["environment", "Subject", "map", "SettingsService", "constructor", "_http", "_loadingService", "_trans", "type", "key", "props", "translate", "label", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "upload_url", "apiUrl", "dir", "accept", "maxFileSize", "useCropperDialog", "config", "width", "height", "resizableArea", "output", "test_image", "instant", "options", "value", "defaultValue", "placeholder", "expressions", "hide", "closeOnSelect", "customFieldsSubject", "subscribe", "res", "customFieldsValue", "initSettingsSubject", "initSettingsValue", "console", "log", "get<PERSON>ustom<PERSON>ields", "custom_fields", "localStorage", "getItem", "next", "JSON", "parse", "getInitSettings", "customFields", "asObservable", "initSettings", "getSettingsData", "systemSettingsSubject", "refreshSettings", "getSettings", "getInitSettingFromLocalStorage", "from_storage", "get", "fields", "custom_fields_default", "defaultFields", "hasOwnProperty", "for<PERSON>ach", "element", "concat", "setItem", "stringify", "err", "show", "updateSettings", "data", "form_data", "FormData", "append", "post", "pipe", "getRequiredVersion", "getNotificationSettings", "getSettingNoAuth", "postAssignPolicy", "user_id", "params", "getMetadaSettings", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\settings.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { LoadingService } from './loading.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SettingsService {\r\n  customFieldsSubject: Subject<any> = new Subject<any>();\r\n  customFieldsValue: any[] = [];\r\n  initSettingsValue: any = {};\r\n  initSettingsSubject: Subject<any> = new Subject<any>();\r\n  systemSettingsSubject: Subject<any> = new Subject<any>();\r\n\r\n  defaultFields: any = [\r\n    {\r\n      type: 'input',\r\n      key: 'first_name',\r\n      props: {\r\n        translate: true,\r\n        label: 'Surname',\r\n        required: true,\r\n        minLength: 1,\r\n        maxLength: 50,\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'last_name',\r\n      props: {\r\n        translate: true,\r\n        label: 'Other names',\r\n        required: true,\r\n        minLength: 1,\r\n        maxLength: 50,\r\n      },\r\n    },\r\n    {\r\n      type: 'image-cropper',\r\n      key: 'photo',\r\n      props: {\r\n        translate: true,\r\n        label: 'Player photo',\r\n        required: true,\r\n        // upload_url: `${environment.apiUrl}/files/editor`,\r\n        upload_url: `${environment.apiUrl}/s3`,\r\n        dir: 'players',\r\n        accept: 'image/png,image/jpg,image/jpeg',\r\n        maxFileSize: 12000,\r\n        useCropperDialog: true,\r\n        config: {\r\n          width: 600,\r\n          height: 800,\r\n          resizableArea: false,\r\n          output: { width: 600, height: 800 },\r\n        },\r\n        test_image: 'assets/images/example_uploads/avatar.jpg',\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'dob',\r\n      props: {\r\n        translate: true,\r\n        label: this._trans.instant('DOB'),\r\n        required: true,\r\n        type: 'date',\r\n      },\r\n    },\r\n    {\r\n      type: 'radio',\r\n      key: 'gender',\r\n      props: {\r\n        translate: true,\r\n        label: 'Gender',\r\n        required: true,\r\n        options: [\r\n          { label: 'Male', value: 'Male' },\r\n          { label: 'Female', value: 'Female' },\r\n        ],\r\n      },\r\n      defaultValue: 'Male',\r\n    },\r\n    {\r\n      type: 'radio',\r\n      key: 'document_type',\r\n      props: {\r\n        translate: true,\r\n        label: 'Document type',\r\n        required: true,\r\n        type: 'select',\r\n        options: [\r\n          { value: 'HKID', label: 'HKID' },\r\n          { value: 'Passport', label: 'Passport' },\r\n        ],\r\n      },\r\n      defaultValue: 'HKID',\r\n    },\r\n    {\r\n      type: 'image-cropper',\r\n      key: 'document_photo',\r\n      props: {\r\n        translate: true,\r\n        label: 'Document photo',\r\n        required: true,\r\n        upload_url: `${environment.apiUrl}/s3`,\r\n        dir: 'player_documents',\r\n        accept: 'image/png,image/jpg,image/jpeg',\r\n        maxFileSize: 12000,\r\n        useCropperDialog: true,\r\n        config: {\r\n          width: 900,\r\n          height: 600,\r\n          resizableArea: false,\r\n          output: { width: 900, height: 600 },\r\n        },\r\n        test_image: 'assets/images/example_uploads/hkid.jpg',\r\n      },\r\n    },\r\n    {\r\n      type: 'custom-date',\r\n      key: 'document_expiry_date',\r\n      props: {\r\n        translate: true,\r\n        label: 'Document expiry date',\r\n        required: false,\r\n        placeholder: 'dd/mm/yyyy',\r\n      },\r\n      expressions: {\r\n        'props.required': \"model.document_type === 'Passport'\",\r\n        hide: \"model.document_type === 'HKID'\",\r\n      },\r\n    },\r\n    {\r\n      type: 'ng-select',\r\n      key: 'club_id',\r\n      props: {\r\n        translate: true,\r\n        label: 'Club',\r\n        required: true,\r\n        closeOnSelect: true,\r\n        options: [],\r\n        placeholder: 'Select Club',\r\n      },\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    public _http: HttpClient,\r\n    public _loadingService: LoadingService,\r\n    public _trans: TranslateService\r\n  ) {\r\n    this.customFieldsSubject.subscribe((res) => {\r\n      this.customFieldsValue = res;\r\n    });\r\n    this.initSettingsSubject.subscribe((res) => {\r\n      this.initSettingsValue = res;\r\n      console.log('initSettingsValue: ', this.initSettingsValue);\r\n    });\r\n  }\r\n\r\n  getCustomFields() {\r\n    let custom_fields = localStorage.getItem('custom_fields');\r\n    if (custom_fields) {\r\n      this.customFieldsSubject.next(JSON.parse(custom_fields));\r\n    } else {\r\n      this.getInitSettings();\r\n    }\r\n  }\r\n\r\n  get customFields() {\r\n    return this.customFieldsSubject.asObservable();\r\n  }\r\n\r\n  get initSettings() {\r\n    return this.initSettingsSubject.asObservable();\r\n  }\r\n\r\n  getSettingsData() {\r\n    return this.systemSettingsSubject.asObservable();\r\n  }\r\n\r\n  refreshSettings() {\r\n    this.getSettings();\r\n  }\r\n\r\n  getInitSettingFromLocalStorage() {\r\n    let initSettings = localStorage.getItem('initSettings');\r\n    if (initSettings) {\r\n      return JSON.parse(initSettings);\r\n    } else {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  getInitSettings(from_storage = true) {\r\n    if (from_storage) {\r\n      let initSettings = localStorage.getItem('initSettings');\r\n      if (initSettings) {\r\n        initSettings = JSON.parse(initSettings);\r\n        this.initSettingsSubject.next(initSettings);\r\n        return this.initSettingsSubject;\r\n      } else {\r\n        from_storage = false;\r\n      }\r\n    }\r\n\r\n    if (!from_storage) {\r\n      return this._http\r\n        .get(`${environment.apiUrl}/settings/init-json`)\r\n        .subscribe(\r\n          (res: any) => {\r\n            let fields = res.custom_fields_default\r\n              ? res.custom_fields_default\r\n              : this.defaultFields;\r\n            if (res.hasOwnProperty('custom_fields') && res.custom_fields) {\r\n              res.custom_fields.forEach((element) => {\r\n                if (element.type == 'image-cropper') {\r\n                  element.props.upload_url = `${environment.apiUrl}/s3`;\r\n                }\r\n              });\r\n              // concat default fields with custom fields\r\n              fields = fields.concat(res.custom_fields);\r\n            }\r\n            // console.log('fields: ',fields);\r\n\r\n            // replace field with upload_url\r\n            fields.forEach((element) => {\r\n              if (element.type == 'image-cropper') {\r\n                element.props.upload_url = `${environment.apiUrl}/s3`;\r\n              }\r\n            });\r\n            localStorage.setItem('custom_fields', JSON.stringify(fields));\r\n            localStorage.setItem('initSettings', JSON.stringify(res));\r\n            this.customFieldsSubject.next(fields);\r\n            this.initSettingsSubject.next(res);\r\n          },\r\n          (err) => {\r\n            console.log(err);\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  getSettings() {\r\n    this._loadingService.show();\r\n    console.log('getSettings');\r\n    return this._http.get(`${environment.apiUrl}/settings`).subscribe(\r\n      (res: any) => {\r\n        this.systemSettingsSubject.next(res);\r\n      },\r\n      (err) => {\r\n        console.log(err);\r\n      }\r\n    );\r\n  }\r\n\r\n  updateSettings(key, data: any) {\r\n    let form_data = new FormData();\r\n    form_data.append('key', key);\r\n    form_data.append('value', JSON.stringify(data));\r\n\r\n    return this._http.post(`${environment.apiUrl}/settings`, form_data).pipe(\r\n      map((res: any) => {\r\n        this.systemSettingsSubject.next(res);\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  getRequiredVersion() {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/settings/required-version`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getNotificationSettings() {\r\n    return this._http.get(`${environment.apiUrl}/settings/notifications`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  getSettingNoAuth() {\r\n    return this._http.get(`${environment.apiUrl}/settings-no-auth`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  postAssignPolicy(user_id) {\r\n    const params = { user_id: user_id };\r\n    return this._http\r\n      .post(`${environment.apiUrl}/settings-no-auth/accept-policy`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getMetadaSettings() {\r\n    return this._http.get(`${environment.apiUrl}/settings/metadata`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}