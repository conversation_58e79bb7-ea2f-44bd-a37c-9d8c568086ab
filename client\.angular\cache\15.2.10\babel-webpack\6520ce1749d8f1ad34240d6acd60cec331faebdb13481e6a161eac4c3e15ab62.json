{"ast": null, "code": "import { ContentHeaderModule } from './layout/components/content-header/content-header.module';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { RouterModule } from '@angular/router';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { LightgalleryModule } from 'lightgallery/angular';\nimport { HttpClient, HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport 'hammerjs';\nimport { NgbCollapseModule, NgbModule, NgbProgressbarModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\nimport { TranslateLoader, TranslateModule } from '@ngx-translate/core';\nimport { ToastrModule } from 'ngx-toastr'; // For auth after login toast\nimport { CoreModule } from '@core/core.module';\nimport { CoreCommonModule } from '@core/common.module';\nimport { CoreSidebarModule, CoreThemeCustomizerModule } from '@core/components';\nimport { AppConfig, coreConfig } from 'app/app-config';\nimport { AppComponent } from 'app/app.component';\nimport { LayoutModule } from 'app/layout/layout.module';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { AuthenticationModule } from './pages/auth/auth.module';\nimport { AuthGuard } from './guards/auth.guard';\nimport { ErrorInterceptor, JwtInterceptor } from './helpers';\nimport { ServiceWorkerModule } from '@angular/service-worker';\nimport { environment } from 'environments/environment';\nimport { ErrorMessageModule } from './layout/components/error-message/error-message.module';\nimport { StyleRenderer, LyTheme2, LyCommonModule, LY_THEME_NAME, LY_THEME } from '@alyle/ui';\nimport { CropperWithDialogModule } from './components/cropper-dialog/cropper-with-dialog.module';\nimport { Color } from '@alyle/ui/color';\nimport { MinimaLight, MinimaDeepDark, MinimaDark } from '@alyle/ui/themes/minima';\nimport { ContentBackgroundModule } from './layout/components/content-background/content-background.module';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { FcmService } from './services/fcm.service';\nimport { AngularFireMessagingModule } from '@angular/fire/compat/messaging';\nimport { AngularFireModule } from '@angular/fire/compat';\nimport { ProfileModule } from './pages/profile/profile.module';\nimport { VerifyTwofaModule } from './components/verify-twofa/verify-twofa.module';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { WatchRoomComponent } from './pages/watch-room/watch-room.component';\nimport { WebcamModule } from 'ngx-webcam';\nimport { StreamingModule } from './pages/streaming/streaming.module';\nimport { VgCoreModule } from '@videogular/ngx-videogular/core';\nimport { VgControlsModule } from '@videogular/ngx-videogular/controls';\nimport { VgOverlayPlayModule } from '@videogular/ngx-videogular/overlay-play';\nimport { VgBufferingModule } from '@videogular/ngx-videogular/buffering';\nimport { VgStreamingModule } from '@videogular/ngx-videogular/streaming';\nimport { NotificationsService } from './layout/components/navbar/navbar-notification/notifications.service';\nimport { SettingsComponent } from './pages/settings/settings.component';\nimport { CoreDirectivesModule } from '@core/directives/directives';\nimport { CommonModule } from '@angular/common';\nimport { DataTablesModule } from 'angular-datatables';\nimport { EditorSidebarModule, serverValidationMessage } from './components/editor-sidebar/editor-sidebar.module';\nimport { BtnDropdownActionModule } from './components/btn-dropdown-action/btn-dropdown-action.module';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { MatchCardModule } from './components/match-card/match-card.module';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { UpdateModalComponent } from './components/update-modal/update-modal.component';\nimport { ReleasesComponent } from './pages/releases/releases.component';\nimport { CheckUpdateComponent } from './pages/settings/check-update/check-update.component';\nimport { HostListenersModule } from './hostlisteners/host-listeners.module';\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { getFirestore, provideFirestore } from '@angular/fire/firestore';\nimport { ErrorComponent } from './pages/error/error.component';\nimport { ReportsComponent } from './pages/reports/reports.component';\nimport { PermissionsGuard } from './guards/permissions.guard';\nimport { OverLaysModule } from './components/overlays/overlays.module';\nimport { RepeateFormTypeComponent } from './components/repeate-form-type/repeate-form-type.component';\nimport { CardSeasonComponent } from './components/card-season/card-season.component';\nimport { TutorialComponent } from './pages/home/<USER>/tutorial.component';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { ContentsComponent } from './pages/settings/contents/contents.component';\nimport { AboutComponent } from './pages/about/about.component';\nimport { WeatherPolicyComponent } from './pages/weather-policy/weather-policy.component';\nimport { CodeConductComponent } from './pages/code-conduct/code-conduct.component';\nimport { DashboardComponent } from './pages/home/<USER>/dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat\";\nimport * as i2 from \"@angular/fire/app\";\nimport * as i3 from \"@angular/fire/firestore\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@angular/service-worker\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"../@core/core.module\";\nimport * as i9 from \"@ngx-formly/core\";\nexport class CustomMinimaLight {\n  constructor() {\n    this.name = 'minima-light';\n    this.demoBg = new Color(0x8c8c8c);\n  }\n  static #_ = this.ɵfac = function CustomMinimaLight_Factory(t) {\n    return new (t || CustomMinimaLight)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomMinimaLight,\n    factory: CustomMinimaLight.ɵfac\n  });\n}\nconst appRoutes = [{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n}, {\n  path: 'home',\n  component: HomeComponent,\n  canActivate: [AuthGuard],\n  data: {\n    animation: 'home'\n  }\n}, {\n  path: 'registration',\n  loadChildren: () => import('./pages/registration/registration.module').then(m => m.RegistrationModule)\n}, {\n  path: 'admin-registration',\n  loadChildren: () => import('./pages/admin-registration/admin-registration.module').then(m => m.AdminRegistrationModule)\n}, {\n  path: 'teams',\n  loadChildren: () => import('./pages/teams/teams.module').then(m => m.TeamModule)\n}, {\n  path: 'leagues',\n  loadChildren: () => import('./pages/league-tournament/league-tournament.module').then(m => m.LeagueTournamentModule)\n}, {\n  path: 'reports',\n  component: ReportsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.registrations_report\n  }\n}, {\n  path: 'streaming',\n  loadChildren: () => import('./pages/streaming/streaming.module').then(m => m.StreamingModule)\n}, {\n  path: 'photo-gallery',\n  loadChildren: () => import('./pages/photo-gallery/photo-gallery.module').then(m => m.PhotoGalleryModule)\n}, {\n  path: 'tables',\n  loadChildren: () => import('./pages/tables/tables.module').then(m => m.TablesModule)\n}, {\n  path: 'profile',\n  loadChildren: () => import('./pages/profile/profile.module').then(m => m.ProfileModule)\n}, {\n  path: 'messages',\n  loadChildren: () => import('./pages/messages/messages.module').then(m => m.MessagesModule)\n}, {\n  path: 'streaming/:match_id/watch/:id',\n  component: WatchRoomComponent\n}, {\n  path: 'watch-room',\n  component: WatchRoomComponent\n}, {\n  path: 'settings',\n  component: SettingsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_settings\n  }\n}, {\n  path: 'check-update',\n  component: CheckUpdateComponent\n}, {\n  path: 'releases',\n  component: ReleasesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_settings\n  }\n}, {\n  path: 'contents',\n  component: ContentsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_settings\n  }\n}, {\n  path: 'email-templates',\n  data: {\n    permissions: AppConfig.PERMISSIONS.email_templates\n  },\n  loadChildren: () => import('./pages/settings/email-templates/email-templates.module').then(m => m.EmailTemplatesModule)\n}, {\n  path: 'manage-sponsors',\n  data: {\n    permissions: AppConfig.PERMISSIONS.sponsors\n  },\n  loadChildren: () => import('./pages/settings/manage-sponsors/manage-sponsors.module').then(m => m.ManageSponsorsModule)\n}, {\n  path: 'about',\n  component: AboutComponent\n}, {\n  path: 'weather-policy',\n  component: WeatherPolicyComponent\n}, {\n  path: 'code-conduct',\n  component: CodeConductComponent\n}, {\n  path: 'sponsor',\n  loadChildren: () => import('./pages/sponsor/sponsor.module').then(m => m.SponsorModule)\n}, {\n  path: '404',\n  component: ErrorComponent\n}, {\n  path: 'payments',\n  loadChildren: () => import('./pages/payments/payments.module').then(m => m.PaymentsModule)\n}, {\n  path: 'players',\n  loadChildren: () => import('./pages/players/players.module').then(m => m.PlayersModule)\n}, {\n  path: '**',\n  redirectTo: '/404'\n}];\nexport class AppModule {\n  constructor() {\n    document.documentElement.style.setProperty('--primary', 'red');\n  }\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [FcmService, LyTheme2, StyleRenderer, NotificationsService, {\n      provide: LY_THEME_NAME,\n      useValue: 'minima-light'\n    }, {\n      provide: LY_THEME,\n      useClass: MinimaLight,\n      multi: true\n    }, {\n      provide: LY_THEME,\n      useClass: MinimaDeepDark,\n      multi: true\n    }, {\n      provide: LY_THEME,\n      useClass: MinimaDark,\n      multi: true\n    }, {\n      provide: LY_THEME,\n      useClass: CustomMinimaLight,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: ErrorInterceptor,\n      multi: true\n    }],\n    imports: [RouterModule, HostListenersModule, CommonModule, WebcamModule, NgSelectModule, AngularFireModule.initializeApp(environment.firebase), provideFirebaseApp(() => initializeApp(environment.firebase)), provideFirestore(() => getFirestore()), ProfileModule, AngularFireMessagingModule, BrowserModule, OverLaysModule, BrowserAnimationsModule, HttpClientModule, VgStreamingModule, DragDropModule, RouterModule.forRoot(appRoutes, {\n      scrollPositionRestoration: 'enabled'\n    }), TranslateModule.forRoot({\n      loader: {\n        provide: TranslateLoader,\n        useFactory: httpTranslateLoader,\n        deps: [HttpClient]\n      }\n    }), ServiceWorkerModule.register('ngsw-worker.js', {\n      enabled: true,\n      // Register the ServiceWorker as soon as the app is stable\n      // or after 30 seconds (whichever comes first).\n      registrationStrategy: 'registerWhenStable:30000'\n    }), ServiceWorkerModule.register('firebase-messaging-sw.js', {\n      enabled: true\n    }),\n    //NgBootstrap\n    NgbModule, NgbProgressbarModule, ToastrModule.forRoot({\n      timeOut: 3000,\n      positionClass: 'toast-top-right',\n      preventDuplicates: true\n    }),\n    // Core modules\n    CoreModule.forRoot(coreConfig), CoreCommonModule, CoreSidebarModule, CoreThemeCustomizerModule, CoreTouchspinModule, CoreDirectivesModule,\n    // App modules\n    StreamingModule, LayoutModule, AuthenticationModule, ErrorMessageModule, ContentHeaderModule, ContentBackgroundModule, VerifyTwofaModule,\n    // Alyle UI modules\n    CropperWithDialogModule, LyCommonModule,\n    // Ngx-videogular\n    VgCoreModule, VgControlsModule, VgOverlayPlayModule, VgBufferingModule,\n    //light gallery\n    LightgalleryModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, FormlyModule.forRoot({\n      types: [],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    })]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, HomeComponent, WatchRoomComponent, SettingsComponent, UpdateModalComponent, ReleasesComponent, CheckUpdateComponent, ReportsComponent, ErrorComponent, RepeateFormTypeComponent, CardSeasonComponent, TutorialComponent, ContentsComponent, AboutComponent, WeatherPolicyComponent, CodeConductComponent, DashboardComponent],\n    imports: [RouterModule, HostListenersModule, CommonModule, WebcamModule, NgSelectModule, i1.AngularFireModule, i2.FirebaseAppModule, i3.FirestoreModule, ProfileModule, AngularFireMessagingModule, BrowserModule, OverLaysModule, BrowserAnimationsModule, HttpClientModule, VgStreamingModule, DragDropModule, i4.RouterModule, i5.TranslateModule, i6.ServiceWorkerModule, i6.ServiceWorkerModule,\n    //NgBootstrap\n    NgbModule, NgbProgressbarModule, i7.ToastrModule, i8.CoreModule, CoreCommonModule, CoreSidebarModule, CoreThemeCustomizerModule, CoreTouchspinModule, CoreDirectivesModule,\n    // App modules\n    StreamingModule, LayoutModule, AuthenticationModule, ErrorMessageModule, ContentHeaderModule, ContentBackgroundModule, VerifyTwofaModule,\n    // Alyle UI modules\n    CropperWithDialogModule, LyCommonModule,\n    // Ngx-videogular\n    VgCoreModule, VgControlsModule, VgOverlayPlayModule, VgBufferingModule,\n    //light gallery\n    LightgalleryModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, i9.FormlyModule]\n  });\n})();\nexport function platform(platform) {\n  return platform;\n}\n// AOT compilation support\nexport function httpTranslateLoader(http) {\n  return new TranslateHttpLoader(http);\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0DAA0D;AAE9F,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SACEC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,QACZ,sBAAsB;AAE7B,OAAO,UAAU;AACjB,SACEC,iBAAiB,EACjBC,SAAS,EACTC,oBAAoB,QACf,4BAA4B;AACnC,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,eAAe,EAAEC,eAAe,QAAQ,qBAAqB;AACtE,SAASC,YAAY,QAAQ,YAAY,CAAC,CAAC;AAE3C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,iBAAiB,EAAEC,yBAAyB,QAAQ,kBAAkB;AAE/E,SAASC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEtD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,WAAW;AAC5D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,kBAAkB,QAAQ,wDAAwD;AAC3F,SACEC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACbC,QAAQ,QACH,WAAW;AAClB,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SACEC,WAAW,EACXC,cAAc,EACdC,UAAU,QACL,yBAAyB;AAChC,SAASC,uBAAuB,QAAQ,kEAAkE;AAC1G,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,YAAY,QAAQ,YAAY;AAEzC,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,oBAAoB,QAAQ,sEAAsE;AAC3G,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,mDAAmD;AAC1D,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,YAAY,QAAQ,kBAAkB;AAI/C,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,mBAAmB,QAAQ,uCAAuC;AAE3E,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,yBAAyB;AACxE,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,SAASC,kBAAkB,QAAQ,4CAA4C;;;;;;;;;;;AAK/E,OAAM,MAAOC,iBAAiB;EAD9BC;IAEE,SAAI,GAAG,cAAc;IACrB,WAAM,GAAG,IAAIrD,KAAK,CAAC,QAAQ,CAAC;;EAC7B;qBAHYoD,iBAAiB;EAAA;EAAA;WAAjBA,iBAAiB;IAAAE,SAAjBF,iBAAiB;EAAA;;AAK9B,MAAMG,SAAS,GAAW,CACxB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,SAAS,EAAEzE,aAAa;EACxB0E,WAAW,EAAE,CAACxE,SAAS,CAAC;EACxByE,IAAI,EAAE;IAAEC,SAAS,EAAE;EAAM;CAC1B,EACD;EACEN,IAAI,EAAE,cAAc;EACpBO,YAAY,EAAE,MACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACC,kBAAkB;CAEhC,EACD;EACEV,IAAI,EAAE,oBAAoB;EAC1BO,YAAY,EAAE,MACZ,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAChEC,CAAC,IAAKA,CAAC,CAACE,uBAAuB;CAErC,EACD;EACEX,IAAI,EAAE,OAAO;EACbO,YAAY,EAAE,MACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,UAAU;CAChE,EACD;EACEZ,IAAI,EAAE,SAAS;EACfO,YAAY,EAAE,MACZ,MAAM,CAAC,oDAAoD,CAAC,CAACC,IAAI,CAC9DC,CAAC,IAAKA,CAAC,CAACI,sBAAsB;CAEpC,EACD;EACEb,IAAI,EAAE,SAAS;EACfG,SAAS,EAAEnB,gBAAgB;EAC3BoB,WAAW,EAAE,CAACnB,gBAAgB,CAAC;EAC/BoB,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACC;EAAoB;CAChE,EACD;EACEhB,IAAI,EAAE,WAAW;EACjBO,YAAY,EAAE,MACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACnD,eAAe;CAE7B,EACD;EACE0C,IAAI,EAAE,eAAe;EACrBO,YAAY,EAAE,MACZ,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CACtDC,CAAC,IAAKA,CAAC,CAACQ,kBAAkB;CAEhC,EACD;EACEjB,IAAI,EAAE,QAAQ;EACdO,YAAY,EAAE,MACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,YAAY;CACpE,EACD;EACElB,IAAI,EAAE,SAAS;EACfO,YAAY,EAAE,MACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxD,aAAa;CACvE,EACD;EACE+C,IAAI,EAAE,UAAU;EAChBO,YAAY,EAAE,MACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,cAAc;CAC1E,EACD;EACEnB,IAAI,EAAE,+BAA+B;EACrCG,SAAS,EAAE/C;CACZ,EACD;EACE4C,IAAI,EAAE,YAAY;EAClBG,SAAS,EAAE/C;CACZ,EACD;EACE4C,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEtC,iBAAiB;EAC5BuC,WAAW,EAAE,CAACnB,gBAAgB,CAAC;EAC/BoB,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACK;EAAe;CAC3D,EACD;EACEpB,IAAI,EAAE,cAAc;EACpBG,SAAS,EAAE1B;CACZ,EACD;EACEuB,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAE3B,iBAAiB;EAC5B4B,WAAW,EAAE,CAACnB,gBAAgB,CAAC;EAC/BoB,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACK;EAAe;CAC3D,EACD;EACEpB,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEZ,iBAAiB;EAC5Ba,WAAW,EAAE,CAACnB,gBAAgB,CAAC;EAC/BoB,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACK;EAAe;CAC3D,EACD;EACEpB,IAAI,EAAE,iBAAiB;EACvBK,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACM;EAAe,CAAE;EAC5Dd,YAAY,EAAE,MACZ,MAAM,CAAC,yDAAyD,CAAC,CAACC,IAAI,CACnEC,CAAC,IAAKA,CAAC,CAACa,oBAAoB;CAElC,EACD;EACEtB,IAAI,EAAE,iBAAiB;EACvBK,IAAI,EAAE;IAAES,WAAW,EAAExF,SAAS,CAACyF,WAAW,CAACQ;EAAQ,CAAE;EACrDhB,YAAY,EAAE,MACZ,MAAM,CAAC,yDAAyD,CAAC,CAACC,IAAI,CACnEC,CAAC,IAAKA,CAAC,CAACe,oBAAoB;CAElC,EACD;EACExB,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEX;CACZ,EACD;EACEQ,IAAI,EAAE,gBAAgB;EACtBG,SAAS,EAAEV;CACZ,EACD;EACEO,IAAI,EAAE,cAAc;EACpBG,SAAS,EAAET;CACZ,EACD;EACEM,IAAI,EAAE,SAAS;EACfO,YAAY,EAAE,MACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACgB,aAAa;CACvE,EACD;EACEzB,IAAI,EAAE,KAAK;EACXG,SAAS,EAAEpB;CACZ,EACD;EACEiB,IAAI,EAAE,UAAU;EAChBO,YAAY,EAAE,MACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACiB,cAAc;CAC1E,EACD;EACE1B,IAAI,EAAE,SAAS;EACfO,YAAY,EAAE,MACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACkB,aAAa;CACvE,EAED;EACE3B,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF;AAuID,OAAM,MAAO2B,SAAS;EACpB/B;IACEgC,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC;EAChE;EAAC;qBAHUJ,SAAS;EAAA;EAAA;UAATA,SAAS;IAAAK,YAHRzG,YAAY;EAAA;EAAA;eAbb,CACTsB,UAAU,EACVX,QAAQ,EACRD,aAAa,EACb0B,oBAAoB,EACpB;MAAEsE,OAAO,EAAE7F,aAAa;MAAE8F,QAAQ,EAAE;IAAc,CAAE,EACpD;MAAED,OAAO,EAAE5F,QAAQ;MAAE8F,QAAQ,EAAE3F,WAAW;MAAE4F,KAAK,EAAE;IAAI,CAAE,EACzD;MAAEH,OAAO,EAAE5F,QAAQ;MAAE8F,QAAQ,EAAE1F,cAAc;MAAE2F,KAAK,EAAE;IAAI,CAAE,EAC5D;MAAEH,OAAO,EAAE5F,QAAQ;MAAE8F,QAAQ,EAAEzF,UAAU;MAAE0F,KAAK,EAAE;IAAI,CAAE,EACxD;MAAEH,OAAO,EAAE5F,QAAQ;MAAE8F,QAAQ,EAAExC,iBAAiB;MAAEyC,KAAK,EAAE;IAAI,CAAE,EAC/D;MAAEH,OAAO,EAAExH,iBAAiB;MAAE0H,QAAQ,EAAEtG,cAAc;MAAEuG,KAAK,EAAE;IAAI,CAAE,EACrE;MAAEH,OAAO,EAAExH,iBAAiB;MAAE0H,QAAQ,EAAEvG,gBAAgB;MAAEwG,KAAK,EAAE;IAAI,CAAE,CACxE;IAAAC,UAzGCjI,YAAY,EACZqE,mBAAmB,EACnBX,YAAY,EACZV,YAAY,EACZF,cAAc,EACdH,iBAAiB,CAAC2B,aAAa,CAAC3C,WAAW,CAACuG,QAAQ,CAAC,EACrD3D,kBAAkB,CAAC,MAAMD,aAAa,CAAC3C,WAAW,CAACuG,QAAQ,CAAC,CAAC,EAC7DzD,gBAAgB,CAAC,MAAMD,YAAY,EAAE,CAAC,EACtC5B,aAAa,EACbF,0BAA0B,EAC1B3C,aAAa,EACb8E,cAAc,EACd5E,uBAAuB,EACvBG,gBAAgB,EAChBkD,iBAAiB,EACjB2B,cAAc,EACdjF,YAAY,CAACmI,OAAO,CAACzC,SAAS,EAAE;MAC9B0C,yBAAyB,EAAE;KAC5B,CAAC,EACFzH,eAAe,CAACwH,OAAO,CAAC;MACtBE,MAAM,EAAE;QACNR,OAAO,EAAEnH,eAAe;QACxB4H,UAAU,EAAEC,mBAAmB;QAC/BC,IAAI,EAAE,CAACrI,UAAU;;KAEpB,CAAC,EAEFuB,mBAAmB,CAAC+G,QAAQ,CAAC,gBAAgB,EAAE;MAC7CC,OAAO,EAAE,IAAI;MACb;MACA;MACAC,oBAAoB,EAAE;KACvB,CAAC,EACFjH,mBAAmB,CAAC+G,QAAQ,CAAC,0BAA0B,EAAE;MACvDC,OAAO,EAAE;KACV,CAAC;IAEF;IACAnI,SAAS,EACTC,oBAAoB,EACpBI,YAAY,CAACuH,OAAO,CAAC;MACnBS,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,iBAAiB;MAChCC,iBAAiB,EAAE;KACpB,CAAC;IAEF;IACAjI,UAAU,CAACsH,OAAO,CAACjH,UAAU,CAAC,EAC9BJ,gBAAgB,EAChBC,iBAAiB,EACjBC,yBAAyB,EACzBwB,mBAAmB,EACnBiB,oBAAoB;IACpB;IACAR,eAAe,EACf7B,YAAY,EACZE,oBAAoB,EACpBM,kBAAkB,EAClB9B,mBAAmB,EACnByC,uBAAuB,EACvBM,iBAAiB;IAEjB;IACAX,uBAAuB,EACvBH,cAAc;IAEd;IACAmB,YAAY,EACZC,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB;IAEjB;IACAnD,kBAAkB,EAElByD,gBAAgB,EAChBb,cAAc,EACdc,mBAAmB,EACnBE,uBAAuB,EACvBtB,mBAAmB,EACnBlC,iBAAiB,EACjByD,qBAAqB,EACrBC,eAAe,EACfC,YAAY,CAACkE,OAAO,CAAC;MACnBY,KAAK,EAAE,EAAE;MACTC,kBAAkB,EAAE,CAClB;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAErF;MAAuB,CAAE;KAE5D,CAAC;EAAA;;;2EAqBO0D,SAAS;IAAA4B,eAjIlBhI,YAAY,EACZE,aAAa,EACb0B,kBAAkB,EAClBS,iBAAiB,EACjBU,oBAAoB,EACpBC,iBAAiB,EACjBC,oBAAoB,EACpBO,gBAAgB,EAChBD,cAAc,EACdI,wBAAwB,EACxBC,mBAAmB,EACnBC,iBAAiB,EACjBE,iBAAiB,EACjBC,cAAc,EACdC,sBAAsB,EACtBC,oBAAoB,EACpBC,kBAAkB;IAAA2C,UAIlBjI,YAAY,EACZqE,mBAAmB,EACnBX,YAAY,EACZV,YAAY,EACZF,cAAc,kEAIdF,aAAa,EACbF,0BAA0B,EAC1B3C,aAAa,EACb8E,cAAc,EACd5E,uBAAuB,EACvBG,gBAAgB,EAChBkD,iBAAiB,EACjB2B,cAAc;IAsBd;IACA1E,SAAS,EACTC,oBAAoB,kCASpBM,gBAAgB,EAChBC,iBAAiB,EACjBC,yBAAyB,EACzBwB,mBAAmB,EACnBiB,oBAAoB;IACpB;IACAR,eAAe,EACf7B,YAAY,EACZE,oBAAoB,EACpBM,kBAAkB,EAClB9B,mBAAmB,EACnByC,uBAAuB,EACvBM,iBAAiB;IAEjB;IACAX,uBAAuB,EACvBH,cAAc;IAEd;IACAmB,YAAY,EACZC,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB;IAEjB;IACAnD,kBAAkB,EAElByD,gBAAgB,EAChBb,cAAc,EACdc,mBAAmB,EACnBE,uBAAuB,EACvBtB,mBAAmB,EACnBlC,iBAAiB,EACjByD,qBAAqB,EACrBC,eAAe;EAAA;AAAA;AAgCnB,OAAM,SAAUoF,QAAQ,CAACA,QAAkB;EACzC,OAAOA,QAAQ;AACjB;AACA;AACA,OAAM,SAAUb,mBAAmB,CAACc,IAAgB;EAClD,OAAO,IAAI5I,mBAAmB,CAAC4I,IAAI,CAAC;AACtC", "names": ["ContentHeaderModule", "BrowserModule", "RouterModule", "BrowserAnimationsModule", "LightgalleryModule", "HttpClient", "HttpClientModule", "HTTP_INTERCEPTORS", "NgbCollapseModule", "NgbModule", "NgbProgressbarModule", "TranslateHttpLoader", "Translate<PERSON><PERSON><PERSON>", "TranslateModule", "ToastrModule", "CoreModule", "CoreCommonModule", "CoreSidebarModule", "CoreThemeCustomizerModule", "AppConfig", "coreConfig", "AppComponent", "LayoutModule", "HomeComponent", "AuthenticationModule", "<PERSON><PERSON><PERSON><PERSON>", "ErrorInterceptor", "JwtInterceptor", "ServiceWorkerModule", "environment", "ErrorMessageModule", "<PERSON><PERSON><PERSON><PERSON>", "LyTheme2", "LyCommonModule", "LY_THEME_NAME", "LY_THEME", "CropperWithDialogModule", "Color", "MinimaLight", "MinimaDeepDark", "MinimaDark", "ContentBackgroundModule", "CoreTouchspinModule", "FcmService", "AngularFireMessagingModule", "AngularFireModule", "ProfileModule", "VerifyTwofaModule", "NgSelectModule", "WatchRoomComponent", "WebcamModule", "StreamingModule", "VgCoreModule", "VgControlsModule", "VgOverlayPlayModule", "VgBufferingModule", "VgStreamingModule", "NotificationsService", "SettingsComponent", "CoreDirectivesModule", "CommonModule", "DataTablesModule", "EditorSidebarModule", "serverValidationMessage", "BtnDropdownActionModule", "FormlyBootstrapModule", "MatchCardModule", "FormlyModule", "UpdateModalComponent", "ReleasesComponent", "CheckUpdateComponent", "HostListenersModule", "initializeApp", "provideFirebaseApp", "getFirestore", "provideFirestore", "ErrorComponent", "ReportsComponent", "PermissionsGuard", "OverLaysModule", "RepeateFormTypeComponent", "CardSeasonComponent", "TutorialComponent", "DragDropModule", "ContentsComponent", "AboutComponent", "WeatherPolicyComponent", "CodeConductComponent", "DashboardComponent", "CustomMinimaLight", "constructor", "factory", "appRoutes", "path", "redirectTo", "pathMatch", "component", "canActivate", "data", "animation", "loadChildren", "then", "m", "RegistrationModule", "AdminRegistrationModule", "TeamModule", "LeagueTournamentModule", "permissions", "PERMISSIONS", "registrations_report", "PhotoGalleryModule", "TablesModule", "MessagesModule", "manage_settings", "email_templates", "EmailTemplatesModule", "sponsors", "ManageSponsorsModule", "SponsorModule", "PaymentsModule", "PlayersModule", "AppModule", "document", "documentElement", "style", "setProperty", "bootstrap", "provide", "useValue", "useClass", "multi", "imports", "firebase", "forRoot", "scrollPositionRestoration", "loader", "useFactory", "httpTranslateLoader", "deps", "register", "enabled", "registrationStrategy", "timeOut", "positionClass", "preventDuplicates", "types", "validationMessages", "name", "message", "declarations", "platform", "http"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\app.module.ts"], "sourcesContent": ["import { ContentHeaderModule } from './layout/components/content-header/content-header.module';\r\nimport { CUSTOM_ELEMENTS_SCHEMA, Injectable, NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { LightgalleryModule } from 'lightgallery/angular';\r\nimport {\r\n  HttpClient,\r\n  HttpClientModule,\r\n  HTTP_INTERCEPTORS,\r\n} from '@angular/common/http';\r\n\r\nimport 'hammerjs';\r\nimport {\r\n  NgbCollapseModule,\r\n  NgbModule,\r\n  NgbProgressbarModule,\r\n} from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\r\nimport { TranslateLoader, TranslateModule } from '@ngx-translate/core';\r\nimport { ToastrModule } from 'ngx-toastr'; // For auth after login toast\r\n\r\nimport { CoreModule } from '@core/core.module';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { CoreSidebarModule, CoreThemeCustomizerModule } from '@core/components';\r\n\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\n\r\nimport { AppComponent } from 'app/app.component';\r\nimport { LayoutModule } from 'app/layout/layout.module';\r\nimport { HomeComponent } from './pages/home/<USER>';\r\nimport { AuthenticationModule } from './pages/auth/auth.module';\r\nimport { AuthGuard } from './guards/auth.guard';\r\nimport { ErrorInterceptor, JwtInterceptor } from './helpers';\r\nimport { ServiceWorkerModule } from '@angular/service-worker';\r\nimport { environment } from 'environments/environment';\r\nimport { ErrorMessageModule } from './layout/components/error-message/error-message.module';\r\nimport {\r\n  StyleRenderer,\r\n  LyTheme2,\r\n  LyCommonModule,\r\n  LY_THEME_NAME,\r\n  LY_THEME,\r\n} from '@alyle/ui';\r\nimport { CropperWithDialogModule } from './components/cropper-dialog/cropper-with-dialog.module';\r\nimport { Color } from '@alyle/ui/color';\r\nimport {\r\n  MinimaLight,\r\n  MinimaDeepDark,\r\n  MinimaDark,\r\n} from '@alyle/ui/themes/minima';\r\nimport { ContentBackgroundModule } from './layout/components/content-background/content-background.module';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\nimport { FcmService } from './services/fcm.service';\r\nimport { AngularFireMessagingModule } from '@angular/fire/compat/messaging';\r\nimport { AngularFireModule } from '@angular/fire/compat';\r\nimport { ProfileModule } from './pages/profile/profile.module';\r\nimport { VerifyTwofaModule } from './components/verify-twofa/verify-twofa.module';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { WatchRoomComponent } from './pages/watch-room/watch-room.component';\r\nimport { WebcamModule } from 'ngx-webcam';\r\nimport { Platform } from '@angular/cdk/platform';\r\nimport { StreamingModule } from './pages/streaming/streaming.module';\r\nimport { VgCoreModule } from '@videogular/ngx-videogular/core';\r\nimport { VgControlsModule } from '@videogular/ngx-videogular/controls';\r\nimport { VgOverlayPlayModule } from '@videogular/ngx-videogular/overlay-play';\r\nimport { VgBufferingModule } from '@videogular/ngx-videogular/buffering';\r\nimport { VgStreamingModule } from '@videogular/ngx-videogular/streaming';\r\nimport { NotificationsService } from './layout/components/navbar/navbar-notification/notifications.service';\r\nimport { SettingsComponent } from './pages/settings/settings.component';\r\nimport { CoreDirectivesModule } from '@core/directives/directives';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from './components/editor-sidebar/editor-sidebar.module';\r\nimport { BtnDropdownActionModule } from './components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { MatchCardModule } from './components/match-card/match-card.module';\r\nimport { FormlyModule } from '@ngx-formly/core';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport { CustomDateParserFormatter } from '@core/services/custom-date-parser-formatter.service';\r\nimport { UpdateModalComponent } from './components/update-modal/update-modal.component';\r\nimport { ReleasesComponent } from './pages/releases/releases.component';\r\nimport { CheckUpdateComponent } from './pages/settings/check-update/check-update.component';\r\nimport { HostListenersModule } from './hostlisteners/host-listeners.module';\r\nimport { KnockoutChartComponent } from './components/knockout-chart/knockout-chart.component';\r\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\r\nimport { getFirestore, provideFirestore } from '@angular/fire/firestore';\r\nimport { ErrorComponent } from './pages/error/error.component';\r\nimport { ReportsComponent } from './pages/reports/reports.component';\r\nimport { PermissionsGuard } from './guards/permissions.guard';\r\nimport { OverLaysModule } from './components/overlays/overlays.module';\r\nimport { RepeateFormTypeComponent } from './components/repeate-form-type/repeate-form-type.component';\r\nimport { CardSeasonComponent } from './components/card-season/card-season.component';\r\nimport { TutorialComponent } from './pages/home/<USER>/tutorial.component';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { ContentsComponent } from './pages/settings/contents/contents.component';\r\nimport { AboutComponent } from './pages/about/about.component';\r\nimport { WeatherPolicyComponent } from './pages/weather-policy/weather-policy.component';\r\nimport { CodeConductComponent } from './pages/code-conduct/code-conduct.component';\r\nimport { DashboardComponent } from './pages/home/<USER>/dashboard.component';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { EmailTemplatesModule } from './pages/settings/email-templates/email-templates.module';\r\n\r\n@Injectable()\r\nexport class CustomMinimaLight {\r\n  name = 'minima-light';\r\n  demoBg = new Color(0x8c8c8c);\r\n}\r\n\r\nconst appRoutes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: '/home',\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: 'home',\r\n    component: HomeComponent,\r\n    canActivate: [AuthGuard],\r\n    data: { animation: 'home' },\r\n  },\r\n  {\r\n    path: 'registration',\r\n    loadChildren: () =>\r\n      import('./pages/registration/registration.module').then(\r\n        (m) => m.RegistrationModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'admin-registration',\r\n    loadChildren: () =>\r\n      import('./pages/admin-registration/admin-registration.module').then(\r\n        (m) => m.AdminRegistrationModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'teams',\r\n    loadChildren: () =>\r\n      import('./pages/teams/teams.module').then((m) => m.TeamModule),\r\n  },\r\n  {\r\n    path: 'leagues',\r\n    loadChildren: () =>\r\n      import('./pages/league-tournament/league-tournament.module').then(\r\n        (m) => m.LeagueTournamentModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'reports',\r\n    component: ReportsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.registrations_report },\r\n  },\r\n  {\r\n    path: 'streaming',\r\n    loadChildren: () =>\r\n      import('./pages/streaming/streaming.module').then(\r\n        (m) => m.StreamingModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'photo-gallery',\r\n    loadChildren: () =>\r\n      import('./pages/photo-gallery/photo-gallery.module').then(\r\n        (m) => m.PhotoGalleryModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'tables',\r\n    loadChildren: () =>\r\n      import('./pages/tables/tables.module').then((m) => m.TablesModule),\r\n  },\r\n  {\r\n    path: 'profile',\r\n    loadChildren: () =>\r\n      import('./pages/profile/profile.module').then((m) => m.ProfileModule),\r\n  },\r\n  {\r\n    path: 'messages',\r\n    loadChildren: () =>\r\n      import('./pages/messages/messages.module').then((m) => m.MessagesModule),\r\n  },\r\n  {\r\n    path: 'streaming/:match_id/watch/:id',\r\n    component: WatchRoomComponent,\r\n  },\r\n  {\r\n    path: 'watch-room',\r\n    component: WatchRoomComponent,\r\n  },\r\n  {\r\n    path: 'settings',\r\n    component: SettingsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_settings },\r\n  },\r\n  {\r\n    path: 'check-update',\r\n    component: CheckUpdateComponent,\r\n  },\r\n  {\r\n    path: 'releases',\r\n    component: ReleasesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_settings },\r\n  },\r\n  {\r\n    path: 'contents',\r\n    component: ContentsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_settings },\r\n  },\r\n  {\r\n    path: 'email-templates',\r\n    data: { permissions: AppConfig.PERMISSIONS.email_templates },\r\n    loadChildren: () =>\r\n      import('./pages/settings/email-templates/email-templates.module').then(\r\n        (m) => m.EmailTemplatesModule\r\n      )\r\n  },\r\n  {\r\n    path: 'manage-sponsors',\r\n    data: { permissions: AppConfig.PERMISSIONS.sponsors },\r\n    loadChildren: () =>\r\n      import('./pages/settings/manage-sponsors/manage-sponsors.module').then(\r\n        (m) => m.ManageSponsorsModule\r\n      ),\r\n  },\r\n  {\r\n    path: 'about',\r\n    component: AboutComponent,\r\n  },\r\n  {\r\n    path: 'weather-policy',\r\n    component: WeatherPolicyComponent,\r\n  },\r\n  {\r\n    path: 'code-conduct',\r\n    component: CodeConductComponent,\r\n  },\r\n  {\r\n    path: 'sponsor',\r\n    loadChildren: () =>\r\n      import('./pages/sponsor/sponsor.module').then((m) => m.SponsorModule),\r\n  },\r\n  {\r\n    path: '404',\r\n    component: ErrorComponent,\r\n  },\r\n  {\r\n    path: 'payments',\r\n    loadChildren: () =>\r\n      import('./pages/payments/payments.module').then((m) => m.PaymentsModule),\r\n  },\r\n  {\r\n    path: 'players',\r\n    loadChildren: () =>\r\n      import('./pages/players/players.module').then((m) => m.PlayersModule),\r\n  },\r\n\r\n  {\r\n    path: '**',\r\n    redirectTo: '/404',\r\n  },\r\n];\r\n\r\nexport type AppThemeVariables = MinimaLight & MinimaDark & CustomMinimaLight;\r\n@NgModule({\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [\r\n    AppComponent,\r\n    HomeComponent,\r\n    WatchRoomComponent,\r\n    SettingsComponent,\r\n    UpdateModalComponent,\r\n    ReleasesComponent,\r\n    CheckUpdateComponent,\r\n    ReportsComponent,\r\n    ErrorComponent,\r\n    RepeateFormTypeComponent,\r\n    CardSeasonComponent,\r\n    TutorialComponent,\r\n    ContentsComponent,\r\n    AboutComponent,\r\n    WeatherPolicyComponent,\r\n    CodeConductComponent,\r\n    DashboardComponent,\r\n\r\n  ],\r\n  imports: [\r\n    RouterModule,\r\n    HostListenersModule,\r\n    CommonModule,\r\n    WebcamModule,\r\n    NgSelectModule,\r\n    AngularFireModule.initializeApp(environment.firebase),\r\n    provideFirebaseApp(() => initializeApp(environment.firebase)),\r\n    provideFirestore(() => getFirestore()),\r\n    ProfileModule,\r\n    AngularFireMessagingModule,\r\n    BrowserModule,\r\n    OverLaysModule,\r\n    BrowserAnimationsModule,\r\n    HttpClientModule,\r\n    VgStreamingModule,\r\n    DragDropModule,\r\n    RouterModule.forRoot(appRoutes, {\r\n      scrollPositionRestoration: 'enabled',\r\n    }),\r\n    TranslateModule.forRoot({\r\n      loader: {\r\n        provide: TranslateLoader,\r\n        useFactory: httpTranslateLoader,\r\n        deps: [HttpClient],\r\n      },\r\n    }),\r\n\r\n    ServiceWorkerModule.register('ngsw-worker.js', {\r\n      enabled: true,\r\n      // Register the ServiceWorker as soon as the app is stable\r\n      // or after 30 seconds (whichever comes first).\r\n      registrationStrategy: 'registerWhenStable:30000',\r\n    }),\r\n    ServiceWorkerModule.register('firebase-messaging-sw.js', {\r\n      enabled: true,\r\n    }),\r\n\r\n    //NgBootstrap\r\n    NgbModule,\r\n    NgbProgressbarModule,\r\n    ToastrModule.forRoot({\r\n      timeOut: 3000,\r\n      positionClass: 'toast-top-right',\r\n      preventDuplicates: true,\r\n    }),\r\n\r\n    // Core modules\r\n    CoreModule.forRoot(coreConfig),\r\n    CoreCommonModule,\r\n    CoreSidebarModule,\r\n    CoreThemeCustomizerModule,\r\n    CoreTouchspinModule,\r\n    CoreDirectivesModule,\r\n    // App modules\r\n    StreamingModule,\r\n    LayoutModule,\r\n    AuthenticationModule,\r\n    ErrorMessageModule,\r\n    ContentHeaderModule,\r\n    ContentBackgroundModule,\r\n    VerifyTwofaModule,\r\n\r\n    // Alyle UI modules\r\n    CropperWithDialogModule,\r\n    LyCommonModule,\r\n\r\n    // Ngx-videogular\r\n    VgCoreModule,\r\n    VgControlsModule,\r\n    VgOverlayPlayModule,\r\n    VgBufferingModule,\r\n\r\n    //light gallery\r\n    LightgalleryModule,\r\n\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    EditorSidebarModule,\r\n    BtnDropdownActionModule,\r\n    CoreTouchspinModule,\r\n    NgbCollapseModule,\r\n    FormlyBootstrapModule,\r\n    MatchCardModule,\r\n    FormlyModule.forRoot({\r\n      types: [],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n\r\n    // CK Editor\r\n    // CKEditorModule,\r\n  ],\r\n  providers: [\r\n    FcmService,\r\n    LyTheme2,\r\n    StyleRenderer,\r\n    NotificationsService,\r\n    { provide: LY_THEME_NAME, useValue: 'minima-light' },\r\n    { provide: LY_THEME, useClass: MinimaLight, multi: true },\r\n    { provide: LY_THEME, useClass: MinimaDeepDark, multi: true },\r\n    { provide: LY_THEME, useClass: MinimaDark, multi: true },\r\n    { provide: LY_THEME, useClass: CustomMinimaLight, multi: true }, // name minima-light\r\n    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },\r\n    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\r\n  ],\r\n  bootstrap: [AppComponent],\r\n  exports: [],\r\n})\r\nexport class AppModule {\r\n  constructor() {\r\n    document.documentElement.style.setProperty('--primary', 'red');\r\n  }\r\n}\r\nexport function platform(platform: Platform) {\r\n  return platform;\r\n}\r\n// AOT compilation support\r\nexport function httpTranslateLoader(http: HttpClient) {\r\n  return new TranslateHttpLoader(http);\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}