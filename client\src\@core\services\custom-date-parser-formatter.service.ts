import { Injectable } from '@angular/core';
import { NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

/**
 * Custom date parser formatter for NgBootstrap date picker
 * Handles dd/mm/yyyy format instead of the default mm/dd/yyyy
 */
@Injectable()
export class CustomDateParserFormatter extends NgbDateParserFormatter {
  readonly DELIMITER = '/';

  /**
   * Parse date string in dd/mm/yyyy format to NgbDateStruct
   * @param value - Date string in dd/mm/yyyy format
   * @returns NgbDateStruct or null if invalid
   */
  parse(value: string): NgbDateStruct | null {
    if (value) {
      const date = value.split(this.DELIMITER);
      if (date.length === 3) {
        const day = parseInt(date[0], 10);
        const month = parseInt(date[1], 10);
        const year = parseInt(date[2], 10);

        // Validate the parsed values
        if (
          !isNaN(day) && day >= 1 && day <= 31 &&
          !isNaN(month) && month >= 1 && month <= 12 &&
          !isNaN(year) && year >= 1000 && year <= 9999
        ) {
          return {
            day: day,
            month: month,
            year: year
          };
        }
      }
    }
    return null;
  }

  /**
   * Format NgbDateStruct to dd/mm/yyyy string
   * @param date - NgbDateStruct to format
   * @returns Formatted date string in dd/mm/yyyy format
   */
  format(date: NgbDateStruct | null): string {
    if (date) {
      // Pad day and month with leading zeros if needed
      const day = date.day.toString().padStart(2, '0');
      const month = date.month.toString().padStart(2, '0');
      const year = date.year.toString();
      
      return `${day}${this.DELIMITER}${month}${this.DELIMITER}${year}`;
    }
    return '';
  }
}
