{"ast": null, "code": "import { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class LocationService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getAllLocations() {\n    return this._http.get(`${environment.apiUrl}/locations/all`);\n  }\n  deleteLocation(locationId) {\n    return this._http.delete(`${environment.apiUrl}/locations/${locationId}`);\n  }\n  static #_ = this.ɵfac = function LocationService_Factory(t) {\n    return new (t || LocationService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LocationService,\n    factory: LocationService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;;;AAKtD,OAAM,MAAOC,eAAe;EAE1BC,YACUC,KAAiB;IAAjB,UAAK,GAALA,KAAK;EAEf;EAEAC,eAAe;IACb,OAAO,IAAI,CAACD,KAAK,CAACE,GAAG,CAAC,GAAGL,WAAW,CAACM,MAAM,gBAAgB,CAAC;EAC9D;EAEAC,cAAc,CAACC,UAAU;IACvB,OAAO,IAAI,CAACL,KAAK,CAACM,MAAM,CAAC,GAAGT,WAAW,CAACM,MAAM,cAAcE,UAAU,EAAE,CAAC;EAC3E;EAAC;qBAbUP,eAAe;EAAA;EAAA;WAAfA,eAAe;IAAAS,SAAfT,eAAe;IAAAU,YAFd;EAAM", "names": ["environment", "LocationService", "constructor", "_http", "getAllLocations", "get", "apiUrl", "deleteLocation", "locationId", "delete", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\location.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class LocationService {\r\n\r\n  constructor(\r\n    private _http: HttpClient\r\n  ) {\r\n  }\r\n\r\n  getAllLocations() {\r\n    return this._http.get(`${environment.apiUrl}/locations/all`);\r\n  }\r\n\r\n  deleteLocation(locationId) {\r\n    return this._http.delete(`${environment.apiUrl}/locations/${locationId}`);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}