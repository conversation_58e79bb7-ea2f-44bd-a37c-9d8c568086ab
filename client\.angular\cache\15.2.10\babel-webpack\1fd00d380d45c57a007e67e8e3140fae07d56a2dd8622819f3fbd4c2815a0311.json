{"ast": null, "code": "/**\r\n * Custom validator for dd/mm/yyyy date format\r\n */\nexport function dateFormatValidator() {\n  return control => {\n    if (!control.value) {\n      return null; // Don't validate empty values\n    }\n\n    const value = control.value;\n    // Check if it's already in ISO format (YYYY-MM-DD) - this is valid\n    if (typeof value === 'string' && /^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n      return isValidISODate(value) ? null : {\n        invalidDate: true\n      };\n    }\n    // Check dd/mm/yyyy format\n    if (typeof value === 'string' && /^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(value)) {\n      return isValidDDMMYYYYDate(value) ? null : {\n        invalidDate: true\n      };\n    }\n    // If it's not in expected format, it's invalid\n    return {\n      invalidDate: true\n    };\n  };\n}\n/**\r\n * Validate ISO date format (YYYY-MM-DD)\r\n */\nfunction isValidISODate(dateString) {\n  const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));\n  const date = new Date(year, month - 1, day);\n  return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;\n}\n/**\r\n * Validate dd/mm/yyyy date format\r\n */\nfunction isValidDDMMYYYYDate(dateString) {\n  const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));\n  const date = new Date(year, month - 1, day);\n  return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;\n}\n/**\r\n * Convert dd/mm/yyyy to ISO format (YYYY-MM-DD)\r\n */\nexport function convertDDMMYYYYToISO(ddmmyyyy) {\n  if (!ddmmyyyy || !/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(ddmmyyyy)) {\n    return ddmmyyyy; // Return as-is if not in expected format\n  }\n\n  const [day, month, year] = ddmmyyyy.split('/');\n  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;\n}\n/**\r\n * Convert ISO format (YYYY-MM-DD) to dd/mm/yyyy\r\n */\nexport function convertISOToDDMMYYYY(isoDate) {\n  if (!isoDate || !/^\\d{4}-\\d{2}-\\d{2}$/.test(isoDate)) {\n    return isoDate; // Return as-is if not in expected format\n  }\n\n  const [year, month, day] = isoDate.split('-');\n  return `${day}/${month}/${year}`;\n}", "map": {"version": 3, "mappings": "AAEA;;;AAGA,OAAM,SAAUA,mBAAmB;EACjC,OAAQC,OAAwB,IAA6B;IAC3D,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE;MAClB,OAAO,IAAI,CAAC,CAAC;;;IAGf,MAAMA,KAAK,GAAGD,OAAO,CAACC,KAAK;IAE3B;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,qBAAqB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;MAClE,OAAOE,cAAc,CAACF,KAAK,CAAC,GAAG,IAAI,GAAG;QAAEG,WAAW,EAAE;MAAI,CAAE;;IAG7D;IACA,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAI,uBAAuB,CAACC,IAAI,CAACD,KAAK,CAAC,EAAE;MACpE,OAAOI,mBAAmB,CAACJ,KAAK,CAAC,GAAG,IAAI,GAAG;QAAEG,WAAW,EAAE;MAAI,CAAE;;IAGlE;IACA,OAAO;MAAEA,WAAW,EAAE;IAAI,CAAE;EAC9B,CAAC;AACH;AAEA;;;AAGA,SAASD,cAAc,CAACG,UAAkB;EACxC,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGH,UAAU,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;EAC9E,MAAME,IAAI,GAAG,IAAIC,IAAI,CAACR,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC;EAE3C,OAAOK,IAAI,CAACE,WAAW,EAAE,KAAKT,IAAI,IAC3BO,IAAI,CAACG,QAAQ,EAAE,KAAKT,KAAK,GAAG,CAAC,IAC7BM,IAAI,CAACI,OAAO,EAAE,KAAKT,GAAG;AAC/B;AAEA;;;AAGA,SAASJ,mBAAmB,CAACC,UAAkB;EAC7C,MAAM,CAACG,GAAG,EAAED,KAAK,EAAED,IAAI,CAAC,GAAGD,UAAU,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;EAC9E,MAAME,IAAI,GAAG,IAAIC,IAAI,CAACR,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC;EAE3C,OAAOK,IAAI,CAACE,WAAW,EAAE,KAAKT,IAAI,IAC3BO,IAAI,CAACG,QAAQ,EAAE,KAAKT,KAAK,GAAG,CAAC,IAC7BM,IAAI,CAACI,OAAO,EAAE,KAAKT,GAAG;AAC/B;AAEA;;;AAGA,OAAM,SAAUU,oBAAoB,CAACC,QAAgB;EACnD,IAAI,CAACA,QAAQ,IAAI,CAAC,uBAAuB,CAAClB,IAAI,CAACkB,QAAQ,CAAC,EAAE;IACxD,OAAOA,QAAQ,CAAC,CAAC;;;EAGnB,MAAM,CAACX,GAAG,EAAED,KAAK,EAAED,IAAI,CAAC,GAAGa,QAAQ,CAACV,KAAK,CAAC,GAAG,CAAC;EAC9C,OAAO,GAAGH,IAAI,IAAIC,KAAK,CAACa,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIZ,GAAG,CAACY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;AACpE;AAEA;;;AAGA,OAAM,SAAUC,oBAAoB,CAACC,OAAe;EAClD,IAAI,CAACA,OAAO,IAAI,CAAC,qBAAqB,CAACrB,IAAI,CAACqB,OAAO,CAAC,EAAE;IACpD,OAAOA,OAAO,CAAC,CAAC;;;EAGlB,MAAM,CAAChB,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGc,OAAO,CAACb,KAAK,CAAC,GAAG,CAAC;EAC7C,OAAO,GAAGD,GAAG,IAAID,KAAK,IAAID,IAAI,EAAE;AAClC", "names": ["dateFormatValidator", "control", "value", "test", "isValidISODate", "invalidDate", "isValidDDMMYYYYDate", "dateString", "year", "month", "day", "split", "map", "num", "parseInt", "date", "Date", "getFullYear", "getMonth", "getDate", "convertDDMMYYYYToISO", "dd<PERSON><PERSON><PERSON>y", "padStart", "convertISOToDDMMYYYY", "isoDate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\@core\\validators\\date-format.validator.ts"], "sourcesContent": ["import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';\n\n/**\n * Custom validator for dd/mm/yyyy date format\n */\nexport function dateFormatValidator(): ValidatorFn {\n  return (control: AbstractControl): ValidationErrors | null => {\n    if (!control.value) {\n      return null; // Don't validate empty values\n    }\n\n    const value = control.value;\n    \n    // Check if it's already in ISO format (YYYY-MM-DD) - this is valid\n    if (typeof value === 'string' && /^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\n      return isValidISODate(value) ? null : { invalidDate: true };\n    }\n\n    // Check dd/mm/yyyy format\n    if (typeof value === 'string' && /^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(value)) {\n      return isValidDDMMYYYYDate(value) ? null : { invalidDate: true };\n    }\n\n    // If it's not in expected format, it's invalid\n    return { invalidDate: true };\n  };\n}\n\n/**\n * Validate ISO date format (YYYY-MM-DD)\n */\nfunction isValidISODate(dateString: string): boolean {\n  const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));\n  const date = new Date(year, month - 1, day);\n  \n  return date.getFullYear() === year &&\n         date.getMonth() === month - 1 &&\n         date.getDate() === day;\n}\n\n/**\n * Validate dd/mm/yyyy date format\n */\nfunction isValidDDMMYYYYDate(dateString: string): boolean {\n  const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));\n  const date = new Date(year, month - 1, day);\n  \n  return date.getFullYear() === year &&\n         date.getMonth() === month - 1 &&\n         date.getDate() === day;\n}\n\n/**\n * Convert dd/mm/yyyy to ISO format (YYYY-MM-DD)\n */\nexport function convertDDMMYYYYToISO(ddmmyyyy: string): string {\n  if (!ddmmyyyy || !/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(ddmmyyyy)) {\n    return ddmmyyyy; // Return as-is if not in expected format\n  }\n\n  const [day, month, year] = ddmmyyyy.split('/');\n  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;\n}\n\n/**\n * Convert ISO format (YYYY-MM-DD) to dd/mm/yyyy\n */\nexport function convertISOToDDMMYYYY(isoDate: string): string {\n  if (!isoDate || !/^\\d{4}-\\d{2}-\\d{2}$/.test(isoDate)) {\n    return isoDate; // Return as-is if not in expected format\n  }\n\n  const [year, month, day] = isoDate.split('-');\n  return `${day}/${month}/${year}`;\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}