import { Component, OnInit } from '@angular/core';
import { FieldType } from '@ngx-formly/bootstrap/form-field';
import { FieldTypeConfig } from '@ngx-formly/core';
import { dateFormatValidator, convertDDMMYYYYToISO, convertISOToDDMMYYYY } from '@core/validators/date-format.validator';

/**
 * Custom date input component for Formly that handles dd/mm/yyyy format
 * This component provides a consistent date input experience across all browsers
 */
@Component({
  selector: 'app-custom-date-input',
  template: `
    <div class="form-group">
      <label *ngIf="props.label" [for]="id">{{ props.label }}</label>
      <input
        [id]="id"
        type="text"
        class="form-control"
        [class.is-invalid]="showError"
        [placeholder]="props.placeholder || 'dd/mm/yyyy'"
        [formControl]="formControl"
        [formlyAttributes]="field"
        [readonly]="props.readonly"
        [disabled]="props.disabled"
        (blur)="onBlur()"
        (input)="onInput($event)"
        maxlength="10"
      />
      <div *ngIf="showError" class="invalid-feedback">
        <formly-validation-message [field]="field"></formly-validation-message>
      </div>
    </div>
  `,
})
export class CustomDateInputComponent extends FieldType<FieldTypeConfig> implements OnInit {

  ngOnInit() {
    // Add date format validator
    if (this.props.required !== false) {
      this.formControl.addValidators(dateFormatValidator());
    }

    // Format initial value if it exists
    if (this.formControl.value) {
      this.formatDisplayValue();
    }
  }

  onInput(event: any) {
    let value = event.target.value;

    // Remove any non-numeric characters except /
    value = value.replace(/[^\d\/]/g, '');

    // Auto-add slashes
    if (value.length >= 2 && value.indexOf('/') === -1) {
      value = value.substring(0, 2) + '/' + value.substring(2);
    }
    if (value.length >= 5 && value.split('/').length === 2) {
      const parts = value.split('/');
      value = parts[0] + '/' + parts[1] + '/' + value.substring(5);
    }

    // Limit to dd/mm/yyyy format
    if (value.length > 10) {
      value = value.substring(0, 10);
    }

    // Update the input field display
    event.target.value = value;

    // Convert to ISO format for form control if valid
    if (this.isValidDate(value)) {
      const isoDate = convertDDMMYYYYToISO(value);
      this.formControl.setValue(isoDate, { emitEvent: false });
    } else if (value.length === 10) {
      // If complete but invalid, mark as invalid
      this.formControl.setErrors({ invalidDate: true });
    } else {
      // Clear errors for incomplete dates
      if (this.formControl.errors?.['invalidDate']) {
        delete this.formControl.errors['invalidDate'];
        if (Object.keys(this.formControl.errors).length === 0) {
          this.formControl.setErrors(null);
        }
      }
    }
  }

  onBlur() {
    this.formatDisplayValue();
  }

  private formatDisplayValue() {
    const value = this.formControl.value;
    if (value && this.isISODateFormat(value)) {
      const displayValue = convertISOToDDMMYYYY(value);
      const inputElement = document.getElementById(this.id) as HTMLInputElement;
      if (inputElement) {
        inputElement.value = displayValue;
      }
    }
  }

  private isValidDate(dateString: string): boolean {
    if (!/^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) {
      return false;
    }

    const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));
    const date = new Date(year, month - 1, day);

    return date.getFullYear() === year &&
      date.getMonth() === month - 1 &&
      date.getDate() === day;
  }

  private isISODateFormat(dateString: string): boolean {
    return /^\d{4}-\d{2}-\d{2}$/.test(dateString);
  }


}
