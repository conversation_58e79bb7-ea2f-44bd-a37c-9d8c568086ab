import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LocationService {

  constructor(
    private _http: HttpClient
  ) {
  }

  getAllLocations() {
    return this._http.get(`${environment.apiUrl}/locations/all`);
  }

  deleteLocation(locationId) {
    return this._http.delete(`${environment.apiUrl}/locations/${locationId}`);
  }
}
