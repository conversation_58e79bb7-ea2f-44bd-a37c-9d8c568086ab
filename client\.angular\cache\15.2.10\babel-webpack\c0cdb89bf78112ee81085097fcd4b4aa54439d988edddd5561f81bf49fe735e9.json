{"ast": null, "code": "import { FieldType } from '@ngx-formly/bootstrap/form-field';\nimport { dateFormatValidator } from '@core/validators/date-format.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngx-formly/core\";\nfunction CustomDateInputComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"for\", ctx_r0.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.props.label);\n  }\n}\nfunction CustomDateInputComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"formly-validation-message\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"field\", ctx_r1.field);\n  }\n}\n/**\r\n * Custom date input component for Formly that handles dd/mm/yyyy format\r\n * This component provides a consistent date input experience across all browsers\r\n */\nexport class CustomDateInputComponent extends FieldType {\n  ngOnInit() {\n    // Add date format validator\n    if (this.props.required !== false) {\n      this.formControl.addValidators(dateFormatValidator());\n    }\n    // Format initial value if it exists\n    if (this.formControl.value) {\n      this.formatDisplayValue();\n    }\n  }\n  onInput(event) {\n    let value = event.target.value;\n    // Remove any non-numeric characters except /\n    value = value.replace(/[^\\d\\/]/g, '');\n    // Auto-add slashes\n    if (value.length >= 2 && value.indexOf('/') === -1) {\n      value = value.substring(0, 2) + '/' + value.substring(2);\n    }\n    if (value.length >= 5 && value.split('/').length === 2) {\n      const parts = value.split('/');\n      value = parts[0] + '/' + parts[1] + '/' + value.substring(5);\n    }\n    // Limit to dd/mm/yyyy format\n    if (value.length > 10) {\n      value = value.substring(0, 10);\n    }\n    // Update the input field display\n    event.target.value = value;\n    // Convert to ISO format for form control if valid\n    if (this.isValidDate(value)) {\n      const isoDate = this.convertToISOFormat(value);\n      this.formControl.setValue(isoDate, {\n        emitEvent: false\n      });\n    } else if (value.length === 10) {\n      // If complete but invalid, mark as invalid\n      this.formControl.setErrors({\n        invalidDate: true\n      });\n    } else {\n      // Clear errors for incomplete dates\n      if (this.formControl.errors?.['invalidDate']) {\n        delete this.formControl.errors['invalidDate'];\n        if (Object.keys(this.formControl.errors).length === 0) {\n          this.formControl.setErrors(null);\n        }\n      }\n    }\n  }\n  onBlur() {\n    this.formatDisplayValue();\n  }\n  formatDisplayValue() {\n    const value = this.formControl.value;\n    if (value && this.isISODateFormat(value)) {\n      const displayValue = this.convertFromISOFormat(value);\n      const inputElement = document.getElementById(this.id);\n      if (inputElement) {\n        inputElement.value = displayValue;\n      }\n    }\n  }\n  isValidDate(dateString) {\n    if (!/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(dateString)) {\n      return false;\n    }\n    const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));\n    const date = new Date(year, month - 1, day);\n    return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;\n  }\n  isISODateFormat(dateString) {\n    return /^\\d{4}-\\d{2}-\\d{2}$/.test(dateString);\n  }\n  convertToISOFormat(ddmmyyyy) {\n    const [day, month, year] = ddmmyyyy.split('/');\n    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;\n  }\n  convertFromISOFormat(isoDate) {\n    const [year, month, day] = isoDate.split('-');\n    return `${day}/${month}/${year}`;\n  }\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵCustomDateInputComponent_BaseFactory;\n    return function CustomDateInputComponent_Factory(t) {\n      return (ɵCustomDateInputComponent_BaseFactory || (ɵCustomDateInputComponent_BaseFactory = i0.ɵɵgetInheritedFactory(CustomDateInputComponent)))(t || CustomDateInputComponent);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CustomDateInputComponent,\n    selectors: [[\"app-custom-date-input\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 4,\n    vars: 10,\n    consts: [[1, \"form-group\"], [3, \"for\", 4, \"ngIf\"], [\"type\", \"text\", \"maxlength\", \"10\", 1, \"form-control\", 3, \"id\", \"placeholder\", \"formControl\", \"formlyAttributes\", \"readonly\", \"disabled\", \"blur\", \"input\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [3, \"for\"], [1, \"invalid-feedback\"], [3, \"field\"]],\n    template: function CustomDateInputComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, CustomDateInputComponent_label_1_Template, 2, 2, \"label\", 1);\n        i0.ɵɵelementStart(2, \"input\", 2);\n        i0.ɵɵlistener(\"blur\", function CustomDateInputComponent_Template_input_blur_2_listener() {\n          return ctx.onBlur();\n        })(\"input\", function CustomDateInputComponent_Template_input_input_2_listener($event) {\n          return ctx.onInput($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, CustomDateInputComponent_div_3_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.props.label);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"is-invalid\", ctx.showError);\n        i0.ɵɵproperty(\"id\", ctx.id)(\"placeholder\", ctx.props.placeholder || \"dd/mm/yyyy\")(\"formControl\", ctx.formControl)(\"formlyAttributes\", ctx.field)(\"readonly\", ctx.props.readonly)(\"disabled\", ctx.props.disabled);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showError);\n      }\n    },\n    dependencies: [i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.MaxLengthValidator, i2.FormControlDirective, i3.ɵFormlyAttributes, i3.ɵFormlyValidationMessage],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,kCAAkC;AAE5D,SAASC,mBAAmB,QAAoD,wCAAwC;;;;;;;IAUlHC,gCAAsC;IAAAA,YAAiB;IAAAA,iBAAQ;;;;IAApCA,+BAAU;IAACA,eAAiB;IAAjBA,wCAAiB;;;;;IAevDA,8BAAgD;IAC9CA,+CAAuE;IACzEA,iBAAM;;;;IADuBA,eAAe;IAAfA,oCAAe;;;AAxBlD;;;;AA6BA,OAAM,MAAOC,wBAAyB,SAAQH,SAA0B;EAEtEI,QAAQ;IACN;IACA,IAAI,IAAI,CAACC,KAAK,CAACC,QAAQ,KAAK,KAAK,EAAE;MACjC,IAAI,CAACC,WAAW,CAACC,aAAa,CAACP,mBAAmB,EAAE,CAAC;;IAGvD;IACA,IAAI,IAAI,CAACM,WAAW,CAACE,KAAK,EAAE;MAC1B,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEAC,OAAO,CAACC,KAAU;IAChB,IAAIH,KAAK,GAAGG,KAAK,CAACC,MAAM,CAACJ,KAAK;IAE9B;IACAA,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAErC;IACA,IAAIL,KAAK,CAACM,MAAM,IAAI,CAAC,IAAIN,KAAK,CAACO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAClDP,KAAK,GAAGA,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,CAAC;;IAE1D,IAAIR,KAAK,CAACM,MAAM,IAAI,CAAC,IAAIN,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAACH,MAAM,KAAK,CAAC,EAAE;MACtD,MAAMI,KAAK,GAAGV,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC;MAC9BT,KAAK,GAAGU,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGV,KAAK,CAACQ,SAAS,CAAC,CAAC,CAAC;;IAG9D;IACA,IAAIR,KAAK,CAACM,MAAM,GAAG,EAAE,EAAE;MACrBN,KAAK,GAAGA,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;IAGhC;IACAL,KAAK,CAACC,MAAM,CAACJ,KAAK,GAAGA,KAAK;IAE1B;IACA,IAAI,IAAI,CAACW,WAAW,CAACX,KAAK,CAAC,EAAE;MAC3B,MAAMY,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACb,KAAK,CAAC;MAC9C,IAAI,CAACF,WAAW,CAACgB,QAAQ,CAACF,OAAO,EAAE;QAAEG,SAAS,EAAE;MAAK,CAAE,CAAC;KACzD,MAAM,IAAIf,KAAK,CAACM,MAAM,KAAK,EAAE,EAAE;MAC9B;MACA,IAAI,CAACR,WAAW,CAACkB,SAAS,CAAC;QAAEC,WAAW,EAAE;MAAI,CAAE,CAAC;KAClD,MAAM;MACL;MACA,IAAI,IAAI,CAACnB,WAAW,CAACoB,MAAM,GAAG,aAAa,CAAC,EAAE;QAC5C,OAAO,IAAI,CAACpB,WAAW,CAACoB,MAAM,CAAC,aAAa,CAAC;QAC7C,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACoB,MAAM,CAAC,CAACZ,MAAM,KAAK,CAAC,EAAE;UACrD,IAAI,CAACR,WAAW,CAACkB,SAAS,CAAC,IAAI,CAAC;;;;EAIxC;EAEAK,MAAM;IACJ,IAAI,CAACpB,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkB;IACxB,MAAMD,KAAK,GAAG,IAAI,CAACF,WAAW,CAACE,KAAK;IACpC,IAAIA,KAAK,IAAI,IAAI,CAACsB,eAAe,CAACtB,KAAK,CAAC,EAAE;MACxC,MAAMuB,YAAY,GAAG,IAAI,CAACC,oBAAoB,CAACxB,KAAK,CAAC;MACrD,MAAMyB,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACC,EAAE,CAAqB;MACzE,IAAIH,YAAY,EAAE;QAChBA,YAAY,CAACzB,KAAK,GAAGuB,YAAY;;;EAGvC;EAEQZ,WAAW,CAACkB,UAAkB;IACpC,IAAI,CAAC,uBAAuB,CAACC,IAAI,CAACD,UAAU,CAAC,EAAE;MAC7C,OAAO,KAAK;;IAGd,MAAM,CAACE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGJ,UAAU,CAACpB,KAAK,CAAC,GAAG,CAAC,CAACyB,GAAG,CAACC,GAAG,IAAIC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;IAC9E,MAAME,IAAI,GAAG,IAAIC,IAAI,CAACL,IAAI,EAAED,KAAK,GAAG,CAAC,EAAED,GAAG,CAAC;IAE3C,OAAOM,IAAI,CAACE,WAAW,EAAE,KAAKN,IAAI,IAChCI,IAAI,CAACG,QAAQ,EAAE,KAAKR,KAAK,GAAG,CAAC,IAC7BK,IAAI,CAACI,OAAO,EAAE,KAAKV,GAAG;EAC1B;EAEQT,eAAe,CAACO,UAAkB;IACxC,OAAO,qBAAqB,CAACC,IAAI,CAACD,UAAU,CAAC;EAC/C;EAEQhB,kBAAkB,CAAC6B,QAAgB;IACzC,MAAM,CAACX,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGS,QAAQ,CAACjC,KAAK,CAAC,GAAG,CAAC;IAC9C,OAAO,GAAGwB,IAAI,IAAID,KAAK,CAACW,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIZ,GAAG,CAACY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACpE;EAEQnB,oBAAoB,CAACZ,OAAe;IAC1C,MAAM,CAACqB,IAAI,EAAED,KAAK,EAAED,GAAG,CAAC,GAAGnB,OAAO,CAACH,KAAK,CAAC,GAAG,CAAC;IAC7C,OAAO,GAAGsB,GAAG,IAAIC,KAAK,IAAIC,IAAI,EAAE;EAClC;EAAC;;;yHA/FUvC,wBAAwB,SAAxBA,wBAAwB;IAAA;EAAA;EAAA;UAAxBA,wBAAwB;IAAAkD;IAAAC;IAAAC;IAAAC;IAAAC;IAAAC;MAAA;QAtBjCxD,8BAAwB;QACtBA,6EAA+D;QAC/DA,gCAaE;QAHAA;UAAA,OAAQyD,YAAQ;QAAA,EAAC;UAAA,OACRA,mBAAe;QAAA,EADP;QAVnBzD,iBAaE;QACFA,yEAEM;QACRA,iBAAM;;;QAlBIA,eAAiB;QAAjBA,sCAAiB;QAKvBA,eAA8B;QAA9BA,2CAA8B;QAH9BA,2BAAS;QAaLA,eAAe;QAAfA,oCAAe", "names": ["FieldType", "dateFormatValidator", "i0", "CustomDateInputComponent", "ngOnInit", "props", "required", "formControl", "addValidators", "value", "formatDisplayValue", "onInput", "event", "target", "replace", "length", "indexOf", "substring", "split", "parts", "isValidDate", "isoDate", "convertToISOFormat", "setValue", "emitEvent", "setErrors", "invalidDate", "errors", "Object", "keys", "onBlur", "isISODateFormat", "displayValue", "convertFromISOFormat", "inputElement", "document", "getElementById", "id", "dateString", "test", "day", "month", "year", "map", "num", "parseInt", "date", "Date", "getFullYear", "getMonth", "getDate", "dd<PERSON><PERSON><PERSON>y", "padStart", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\components\\custom-date-input\\custom-date-input.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FieldType } from '@ngx-formly/bootstrap/form-field';\nimport { FieldTypeConfig } from '@ngx-formly/core';\nimport { dateFormatValidator, convertDDMMYYYYToISO, convertISOToDDMMYYYY } from '@core/validators/date-format.validator';\n\n/**\n * Custom date input component for Formly that handles dd/mm/yyyy format\n * This component provides a consistent date input experience across all browsers\n */\n@Component({\n  selector: 'app-custom-date-input',\n  template: `\n    <div class=\"form-group\">\n      <label *ngIf=\"props.label\" [for]=\"id\">{{ props.label }}</label>\n      <input\n        [id]=\"id\"\n        type=\"text\"\n        class=\"form-control\"\n        [class.is-invalid]=\"showError\"\n        [placeholder]=\"props.placeholder || 'dd/mm/yyyy'\"\n        [formControl]=\"formControl\"\n        [formlyAttributes]=\"field\"\n        [readonly]=\"props.readonly\"\n        [disabled]=\"props.disabled\"\n        (blur)=\"onBlur()\"\n        (input)=\"onInput($event)\"\n        maxlength=\"10\"\n      />\n      <div *ngIf=\"showError\" class=\"invalid-feedback\">\n        <formly-validation-message [field]=\"field\"></formly-validation-message>\n      </div>\n    </div>\n  `,\n})\nexport class CustomDateInputComponent extends FieldType<FieldTypeConfig> implements OnInit {\n\n  ngOnInit() {\n    // Add date format validator\n    if (this.props.required !== false) {\n      this.formControl.addValidators(dateFormatValidator());\n    }\n\n    // Format initial value if it exists\n    if (this.formControl.value) {\n      this.formatDisplayValue();\n    }\n  }\n\n  onInput(event: any) {\n    let value = event.target.value;\n\n    // Remove any non-numeric characters except /\n    value = value.replace(/[^\\d\\/]/g, '');\n\n    // Auto-add slashes\n    if (value.length >= 2 && value.indexOf('/') === -1) {\n      value = value.substring(0, 2) + '/' + value.substring(2);\n    }\n    if (value.length >= 5 && value.split('/').length === 2) {\n      const parts = value.split('/');\n      value = parts[0] + '/' + parts[1] + '/' + value.substring(5);\n    }\n\n    // Limit to dd/mm/yyyy format\n    if (value.length > 10) {\n      value = value.substring(0, 10);\n    }\n\n    // Update the input field display\n    event.target.value = value;\n\n    // Convert to ISO format for form control if valid\n    if (this.isValidDate(value)) {\n      const isoDate = this.convertToISOFormat(value);\n      this.formControl.setValue(isoDate, { emitEvent: false });\n    } else if (value.length === 10) {\n      // If complete but invalid, mark as invalid\n      this.formControl.setErrors({ invalidDate: true });\n    } else {\n      // Clear errors for incomplete dates\n      if (this.formControl.errors?.['invalidDate']) {\n        delete this.formControl.errors['invalidDate'];\n        if (Object.keys(this.formControl.errors).length === 0) {\n          this.formControl.setErrors(null);\n        }\n      }\n    }\n  }\n\n  onBlur() {\n    this.formatDisplayValue();\n  }\n\n  private formatDisplayValue() {\n    const value = this.formControl.value;\n    if (value && this.isISODateFormat(value)) {\n      const displayValue = this.convertFromISOFormat(value);\n      const inputElement = document.getElementById(this.id) as HTMLInputElement;\n      if (inputElement) {\n        inputElement.value = displayValue;\n      }\n    }\n  }\n\n  private isValidDate(dateString: string): boolean {\n    if (!/^\\d{2}\\/\\d{2}\\/\\d{4}$/.test(dateString)) {\n      return false;\n    }\n\n    const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));\n    const date = new Date(year, month - 1, day);\n\n    return date.getFullYear() === year &&\n      date.getMonth() === month - 1 &&\n      date.getDate() === day;\n  }\n\n  private isISODateFormat(dateString: string): boolean {\n    return /^\\d{4}-\\d{2}-\\d{2}$/.test(dateString);\n  }\n\n  private convertToISOFormat(ddmmyyyy: string): string {\n    const [day, month, year] = ddmmyyyy.split('/');\n    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;\n  }\n\n  private convertFromISOFormat(isoDate: string): string {\n    const [year, month, day] = isoDate.split('-');\n    return `${day}/${month}/${year}`;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}