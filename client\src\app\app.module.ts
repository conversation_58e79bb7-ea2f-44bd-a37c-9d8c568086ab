import { ContentHeaderModule } from './layout/components/content-header/content-header.module';
import { CUSTOM_ELEMENTS_SCHEMA, Injectable, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule, Routes } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LightgalleryModule } from 'lightgallery/angular';
import {
  HttpClient,
  HttpClientModule,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';

import 'hammerjs';
import {
  NgbCollapseModule,
  NgbModule,
  NgbProgressbarModule,
} from '@ng-bootstrap/ng-bootstrap';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { ToastrModule } from 'ngx-toastr'; // For auth after login toast

import { CoreModule } from '@core/core.module';
import { CoreCommonModule } from '@core/common.module';
import { CoreSidebarModule, CoreThemeCustomizerModule } from '@core/components';

import { AppConfig, coreConfig } from 'app/app-config';

import { AppComponent } from 'app/app.component';
import { LayoutModule } from 'app/layout/layout.module';
import { HomeComponent } from './pages/home/<USER>';
import { AuthenticationModule } from './pages/auth/auth.module';
import { AuthGuard } from './guards/auth.guard';
import { ErrorInterceptor, JwtInterceptor } from './helpers';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from 'environments/environment';
import { ErrorMessageModule } from './layout/components/error-message/error-message.module';
import {
  StyleRenderer,
  LyTheme2,
  LyCommonModule,
  LY_THEME_NAME,
  LY_THEME,
} from '@alyle/ui';
import { CropperWithDialogModule } from './components/cropper-dialog/cropper-with-dialog.module';
import { Color } from '@alyle/ui/color';
import {
  MinimaLight,
  MinimaDeepDark,
  MinimaDark,
} from '@alyle/ui/themes/minima';
import { ContentBackgroundModule } from './layout/components/content-background/content-background.module';
import { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';
import { FcmService } from './services/fcm.service';
import { AngularFireMessagingModule } from '@angular/fire/compat/messaging';
import { AngularFireModule } from '@angular/fire/compat';
import { ProfileModule } from './pages/profile/profile.module';
import { VerifyTwofaModule } from './components/verify-twofa/verify-twofa.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { WatchRoomComponent } from './pages/watch-room/watch-room.component';
import { WebcamModule } from 'ngx-webcam';
import { Platform } from '@angular/cdk/platform';
import { StreamingModule } from './pages/streaming/streaming.module';
import { VgCoreModule } from '@videogular/ngx-videogular/core';
import { VgControlsModule } from '@videogular/ngx-videogular/controls';
import { VgOverlayPlayModule } from '@videogular/ngx-videogular/overlay-play';
import { VgBufferingModule } from '@videogular/ngx-videogular/buffering';
import { VgStreamingModule } from '@videogular/ngx-videogular/streaming';
import { NotificationsService } from './layout/components/navbar/navbar-notification/notifications.service';
import { SettingsComponent } from './pages/settings/settings.component';
import { CoreDirectivesModule } from '@core/directives/directives';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import {
  EditorSidebarModule,
  serverValidationMessage,
} from './components/editor-sidebar/editor-sidebar.module';
import { BtnDropdownActionModule } from './components/btn-dropdown-action/btn-dropdown-action.module';
import { FormlyBootstrapModule } from '@ngx-formly/bootstrap';
import { MatchCardModule } from './components/match-card/match-card.module';
import { FormlyModule } from '@ngx-formly/core';
import { Capacitor } from '@capacitor/core';
import { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { CustomDateParserFormatter } from '@core/services/custom-date-parser-formatter.service';
import { UpdateModalComponent } from './components/update-modal/update-modal.component';
import { ReleasesComponent } from './pages/releases/releases.component';
import { CheckUpdateComponent } from './pages/settings/check-update/check-update.component';
import { HostListenersModule } from './hostlisteners/host-listeners.module';
import { KnockoutChartComponent } from './components/knockout-chart/knockout-chart.component';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { ErrorComponent } from './pages/error/error.component';
import { ReportsComponent } from './pages/reports/reports.component';
import { PermissionsGuard } from './guards/permissions.guard';
import { OverLaysModule } from './components/overlays/overlays.module';
import { RepeateFormTypeComponent } from './components/repeate-form-type/repeate-form-type.component';
import { CardSeasonComponent } from './components/card-season/card-season.component';
import { TutorialComponent } from './pages/home/<USER>/tutorial.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ContentsComponent } from './pages/settings/contents/contents.component';
import { AboutComponent } from './pages/about/about.component';
import { WeatherPolicyComponent } from './pages/weather-policy/weather-policy.component';
import { CodeConductComponent } from './pages/code-conduct/code-conduct.component';
import { DashboardComponent } from './pages/home/<USER>/dashboard.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { EmailTemplatesModule } from './pages/settings/email-templates/email-templates.module';

@Injectable()
export class CustomMinimaLight {
  name = 'minima-light';
  demoBg = new Color(0x8c8c8c);
}

const appRoutes: Routes = [
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full',
  },
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [AuthGuard],
    data: { animation: 'home' },
  },
  {
    path: 'registration',
    loadChildren: () =>
      import('./pages/registration/registration.module').then(
        (m) => m.RegistrationModule
      ),
  },
  {
    path: 'admin-registration',
    loadChildren: () =>
      import('./pages/admin-registration/admin-registration.module').then(
        (m) => m.AdminRegistrationModule
      ),
  },
  {
    path: 'teams',
    loadChildren: () =>
      import('./pages/teams/teams.module').then((m) => m.TeamModule),
  },
  {
    path: 'leagues',
    loadChildren: () =>
      import('./pages/league-tournament/league-tournament.module').then(
        (m) => m.LeagueTournamentModule
      ),
  },
  {
    path: 'reports',
    component: ReportsComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.registrations_report },
  },
  {
    path: 'streaming',
    loadChildren: () =>
      import('./pages/streaming/streaming.module').then(
        (m) => m.StreamingModule
      ),
  },
  {
    path: 'photo-gallery',
    loadChildren: () =>
      import('./pages/photo-gallery/photo-gallery.module').then(
        (m) => m.PhotoGalleryModule
      ),
  },
  {
    path: 'tables',
    loadChildren: () =>
      import('./pages/tables/tables.module').then((m) => m.TablesModule),
  },
  {
    path: 'profile',
    loadChildren: () =>
      import('./pages/profile/profile.module').then((m) => m.ProfileModule),
  },
  {
    path: 'messages',
    loadChildren: () =>
      import('./pages/messages/messages.module').then((m) => m.MessagesModule),
  },
  {
    path: 'streaming/:match_id/watch/:id',
    component: WatchRoomComponent,
  },
  {
    path: 'watch-room',
    component: WatchRoomComponent,
  },
  {
    path: 'settings',
    component: SettingsComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_settings },
  },
  {
    path: 'check-update',
    component: CheckUpdateComponent,
  },
  {
    path: 'releases',
    component: ReleasesComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_settings },
  },
  {
    path: 'contents',
    component: ContentsComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_settings },
  },
  {
    path: 'email-templates',
    data: { permissions: AppConfig.PERMISSIONS.email_templates },
    loadChildren: () =>
      import('./pages/settings/email-templates/email-templates.module').then(
        (m) => m.EmailTemplatesModule
      )
  },
  {
    path: 'manage-sponsors',
    data: { permissions: AppConfig.PERMISSIONS.sponsors },
    loadChildren: () =>
      import('./pages/settings/manage-sponsors/manage-sponsors.module').then(
        (m) => m.ManageSponsorsModule
      ),
  },
  {
    path: 'about',
    component: AboutComponent,
  },
  {
    path: 'weather-policy',
    component: WeatherPolicyComponent,
  },
  {
    path: 'code-conduct',
    component: CodeConductComponent,
  },
  {
    path: 'sponsor',
    loadChildren: () =>
      import('./pages/sponsor/sponsor.module').then((m) => m.SponsorModule),
  },
  {
    path: '404',
    component: ErrorComponent,
  },
  {
    path: 'payments',
    loadChildren: () =>
      import('./pages/payments/payments.module').then((m) => m.PaymentsModule),
  },
  {
    path: 'players',
    loadChildren: () =>
      import('./pages/players/players.module').then((m) => m.PlayersModule),
  },

  {
    path: '**',
    redirectTo: '/404',
  },
];

export type AppThemeVariables = MinimaLight & MinimaDark & CustomMinimaLight;
@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [
    AppComponent,
    HomeComponent,
    WatchRoomComponent,
    SettingsComponent,
    UpdateModalComponent,
    ReleasesComponent,
    CheckUpdateComponent,
    ReportsComponent,
    ErrorComponent,
    RepeateFormTypeComponent,
    CardSeasonComponent,
    TutorialComponent,
    ContentsComponent,
    AboutComponent,
    WeatherPolicyComponent,
    CodeConductComponent,
    DashboardComponent,

  ],
  imports: [
    RouterModule,
    HostListenersModule,
    CommonModule,
    WebcamModule,
    NgSelectModule,
    AngularFireModule.initializeApp(environment.firebase),
    provideFirebaseApp(() => initializeApp(environment.firebase)),
    provideFirestore(() => getFirestore()),
    ProfileModule,
    AngularFireMessagingModule,
    BrowserModule,
    OverLaysModule,
    BrowserAnimationsModule,
    HttpClientModule,
    VgStreamingModule,
    DragDropModule,
    RouterModule.forRoot(appRoutes, {
      scrollPositionRestoration: 'enabled',
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: httpTranslateLoader,
        deps: [HttpClient],
      },
    }),

    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: true,
      // Register the ServiceWorker as soon as the app is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000',
    }),
    ServiceWorkerModule.register('firebase-messaging-sw.js', {
      enabled: true,
    }),

    //NgBootstrap
    NgbModule,
    NgbProgressbarModule,
    ToastrModule.forRoot({
      timeOut: 3000,
      positionClass: 'toast-top-right',
      preventDuplicates: true,
    }),

    // Core modules
    CoreModule.forRoot(coreConfig),
    CoreCommonModule,
    CoreSidebarModule,
    CoreThemeCustomizerModule,
    CoreTouchspinModule,
    CoreDirectivesModule,
    // App modules
    StreamingModule,
    LayoutModule,
    AuthenticationModule,
    ErrorMessageModule,
    ContentHeaderModule,
    ContentBackgroundModule,
    VerifyTwofaModule,

    // Alyle UI modules
    CropperWithDialogModule,
    LyCommonModule,

    // Ngx-videogular
    VgCoreModule,
    VgControlsModule,
    VgOverlayPlayModule,
    VgBufferingModule,

    //light gallery
    LightgalleryModule,

    DataTablesModule,
    NgSelectModule,
    EditorSidebarModule,
    BtnDropdownActionModule,
    CoreTouchspinModule,
    NgbCollapseModule,
    FormlyBootstrapModule,
    MatchCardModule,
    FormlyModule.forRoot({
      types: [],
      validationMessages: [
        { name: 'serverError', message: serverValidationMessage },
      ],
    }),

    // CK Editor
    // CKEditorModule,
  ],
  providers: [
    FcmService,
    LyTheme2,
    StyleRenderer,
    NotificationsService,
    { provide: LY_THEME_NAME, useValue: 'minima-light' },
    { provide: LY_THEME, useClass: MinimaLight, multi: true },
    { provide: LY_THEME, useClass: MinimaDeepDark, multi: true },
    { provide: LY_THEME, useClass: MinimaDark, multi: true },
    { provide: LY_THEME, useClass: CustomMinimaLight, multi: true }, // name minima-light
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
  ],
  bootstrap: [AppComponent],
  exports: [],
})
export class AppModule {
  constructor() {
    document.documentElement.style.setProperty('--primary', 'red');
  }
}
export function platform(platform: Platform) {
  return platform;
}
// AOT compilation support
export function httpTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http);
}
