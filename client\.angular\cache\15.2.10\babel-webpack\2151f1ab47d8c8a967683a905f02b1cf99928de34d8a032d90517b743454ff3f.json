{"ast": null, "code": "import { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\n/**\r\n * Custom date parser formatter for NgBootstrap date picker\r\n * Handles dd/mm/yyyy format instead of the default mm/dd/yyyy\r\n */\nexport class CustomDateParserFormatter extends NgbDateParserFormatter {\n  constructor() {\n    super(...arguments);\n    this.DELIMITER = '/';\n  }\n  /**\r\n   * Parse date string in dd/mm/yyyy format to NgbDateStruct\r\n   * @param value - Date string in dd/mm/yyyy format\r\n   * @returns NgbDateStruct or null if invalid\r\n   */\n  parse(value) {\n    if (value) {\n      const date = value.split(this.DELIMITER);\n      if (date.length === 3) {\n        const day = parseInt(date[0], 10);\n        const month = parseInt(date[1], 10);\n        const year = parseInt(date[2], 10);\n        // Validate the parsed values\n        if (!isNaN(day) && day >= 1 && day <= 31 && !isNaN(month) && month >= 1 && month <= 12 && !isNaN(year) && year >= 1000 && year <= 9999) {\n          return {\n            day: day,\n            month: month,\n            year: year\n          };\n        }\n      }\n    }\n    return null;\n  }\n  /**\r\n   * Format NgbDateStruct to dd/mm/yyyy string\r\n   * @param date - NgbDateStruct to format\r\n   * @returns Formatted date string in dd/mm/yyyy format\r\n   */\n  format(date) {\n    if (date) {\n      // Pad day and month with leading zeros if needed\n      const day = date.day.toString().padStart(2, '0');\n      const month = date.month.toString().padStart(2, '0');\n      const year = date.year.toString();\n      return `${day}${this.DELIMITER}${month}${this.DELIMITER}${year}`;\n    }\n    return '';\n  }\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵCustomDateParserFormatter_BaseFactory;\n    return function CustomDateParserFormatter_Factory(t) {\n      return (ɵCustomDateParserFormatter_BaseFactory || (ɵCustomDateParserFormatter_BaseFactory = i0.ɵɵgetInheritedFactory(CustomDateParserFormatter)))(t || CustomDateParserFormatter);\n    };\n  }();\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomDateParserFormatter,\n    factory: CustomDateParserFormatter.ɵfac\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,sBAAsB,QAAuB,4BAA4B;;AAElF;;;;AAKA,OAAM,MAAOC,yBAA0B,SAAQD,sBAAsB;EADrEE;;IAEW,cAAS,GAAG,GAAG;;EAExB;;;;;EAKAC,KAAK,CAACC,KAAa;IACjB,IAAIA,KAAK,EAAE;MACT,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,CAAC,IAAI,CAACC,SAAS,CAAC;MACxC,IAAIF,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMC,GAAG,GAAGC,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACjC,MAAMM,KAAK,GAAGD,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACnC,MAAMO,IAAI,GAAGF,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAElC;QACA,IACE,CAACQ,KAAK,CAACJ,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,EAAE,IACpC,CAACI,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE,IAC1C,CAACE,KAAK,CAACD,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,IAAI,EAC5C;UACA,OAAO;YACLH,GAAG,EAAEA,GAAG;YACRE,KAAK,EAAEA,KAAK;YACZC,IAAI,EAAEA;WACP;;;;IAIP,OAAO,IAAI;EACb;EAEA;;;;;EAKAE,MAAM,CAACT,IAA0B;IAC/B,IAAIA,IAAI,EAAE;MACR;MACA,MAAMI,GAAG,GAAGJ,IAAI,CAACI,GAAG,CAACM,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAChD,MAAML,KAAK,GAAGN,IAAI,CAACM,KAAK,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACpD,MAAMJ,IAAI,GAAGP,IAAI,CAACO,IAAI,CAACG,QAAQ,EAAE;MAEjC,OAAO,GAAGN,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGI,KAAK,GAAG,IAAI,CAACJ,SAAS,GAAGK,IAAI,EAAE;;IAElE,OAAO,EAAE;EACX;EAAC;;;2HAhDUX,yBAAyB,SAAzBA,yBAAyB;IAAA;EAAA;EAAA;WAAzBA,yBAAyB;IAAAgB,SAAzBhB,yBAAyB;EAAA", "names": ["NgbDateParserFormatter", "CustomDateParserFormatter", "constructor", "parse", "value", "date", "split", "DELIMITER", "length", "day", "parseInt", "month", "year", "isNaN", "format", "toString", "padStart", "factory"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\@core\\services\\custom-date-parser-formatter.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';\n\n/**\n * Custom date parser formatter for NgBootstrap date picker\n * Handles dd/mm/yyyy format instead of the default mm/dd/yyyy\n */\n@Injectable()\nexport class CustomDateParserFormatter extends NgbDateParserFormatter {\n  readonly DELIMITER = '/';\n\n  /**\n   * Parse date string in dd/mm/yyyy format to NgbDateStruct\n   * @param value - Date string in dd/mm/yyyy format\n   * @returns NgbDateStruct or null if invalid\n   */\n  parse(value: string): NgbDateStruct | null {\n    if (value) {\n      const date = value.split(this.DELIMITER);\n      if (date.length === 3) {\n        const day = parseInt(date[0], 10);\n        const month = parseInt(date[1], 10);\n        const year = parseInt(date[2], 10);\n\n        // Validate the parsed values\n        if (\n          !isNaN(day) && day >= 1 && day <= 31 &&\n          !isNaN(month) && month >= 1 && month <= 12 &&\n          !isNaN(year) && year >= 1000 && year <= 9999\n        ) {\n          return {\n            day: day,\n            month: month,\n            year: year\n          };\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Format NgbDateStruct to dd/mm/yyyy string\n   * @param date - NgbDateStruct to format\n   * @returns Formatted date string in dd/mm/yyyy format\n   */\n  format(date: NgbDateStruct | null): string {\n    if (date) {\n      // Pad day and month with leading zeros if needed\n      const day = date.day.toString().padStart(2, '0');\n      const month = date.month.toString().padStart(2, '0');\n      const year = date.year.toString();\n      \n      return `${day}${this.DELIMITER}${month}${this.DELIMITER}${year}`;\n    }\n    return '';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}