import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Custom validator for dd/mm/yyyy date format
 */
export function dateFormatValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values
    }

    const value = control.value;
    
    // Check if it's already in ISO format (YYYY-MM-DD) - this is valid
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
      return isValidISODate(value) ? null : { invalidDate: true };
    }

    // Check dd/mm/yyyy format
    if (typeof value === 'string' && /^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
      return isValidDDMMYYYYDate(value) ? null : { invalidDate: true };
    }

    // If it's not in expected format, it's invalid
    return { invalidDate: true };
  };
}

/**
 * Validate ISO date format (YYYY-MM-DD)
 */
function isValidISODate(dateString: string): boolean {
  const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));
  const date = new Date(year, month - 1, day);
  
  return date.getFullYear() === year &&
         date.getMonth() === month - 1 &&
         date.getDate() === day;
}

/**
 * Validate dd/mm/yyyy date format
 */
function isValidDDMMYYYYDate(dateString: string): boolean {
  const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));
  const date = new Date(year, month - 1, day);
  
  return date.getFullYear() === year &&
         date.getMonth() === month - 1 &&
         date.getDate() === day;
}

/**
 * Convert dd/mm/yyyy to ISO format (YYYY-MM-DD)
 */
export function convertDDMMYYYYToISO(ddmmyyyy: string): string {
  if (!ddmmyyyy || !/^\d{2}\/\d{2}\/\d{4}$/.test(ddmmyyyy)) {
    return ddmmyyyy; // Return as-is if not in expected format
  }

  const [day, month, year] = ddmmyyyy.split('/');
  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
}

/**
 * Convert ISO format (YYYY-MM-DD) to dd/mm/yyyy
 */
export function convertISOToDDMMYYYY(isoDate: string): string {
  if (!isoDate || !/^\d{4}-\d{2}-\d{2}$/.test(isoDate)) {
    return isoDate; // Return as-is if not in expected format
  }

  const [year, month, day] = isoDate.split('-');
  return `${day}/${month}/${year}`;
}
