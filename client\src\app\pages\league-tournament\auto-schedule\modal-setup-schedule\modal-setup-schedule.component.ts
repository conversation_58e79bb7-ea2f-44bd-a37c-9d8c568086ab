import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { TournamentService } from '../../../../services/tournament.service';
import { LocationService } from '../../../../services/location.service';
import { FormGroup } from '@angular/forms';
import { AutoScheduleService } from '../../../../services/auto-schedule.service';
import { LoadingService } from 'app/services/loading.service';
import { forkJoin } from 'rxjs';
import { SeasonService } from 'app/services/season.service';
import moment from "moment";
import { AppConfig } from "../../../../app-config";
import Swal from 'sweetalert2';

@Component({
    selector: 'app-modal-setup-schedule',
    templateUrl: './modal-setup-schedule.component.html',
    styleUrls: ['./modal-setup-schedule.component.scss']
})
export class ModalSetupScheduleComponent {

    @Input() tournamentId: string;
    @Input() tournamentInfo: string;
    @Input() seasonId: string;


    setupScheduleForm = new FormGroup({});
    setupScheduleModel = {};

    listStages = [];
    refereeIds = [];
    listGroups = [];

    public setupScheduleFields: FormlyFieldConfig[] = [
        {
            key: 'list_stages',
            type: 'ng-select',
            props: {
                required: true,
                multiple: false,
                closeOnSelect: true,
                label: 'Stage',
                placeholder: 'Please select a stage to schedule',
                options: []
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Please select a stage to schedule.')
                }
            },
            hooks: {
                onChanges: (field) => {
                    field.form.get('list_stages').valueChanges.subscribe(value => {
                        this.setupScheduleModel['stage_id'] = this.listStages.find(item => item.name === value)?.id || -1;
                    });
                }
            }
        },
        {
            key: 'list_group_names',
            type: 'ng-select',
            props: {
                multiple: true,
                hideOnMultiple: true,
                defaultValue: [],
                label: 'Groups',
                placeholder: 'Select groups to schedule (leave empty to include all)',
                options: [],
                hide: true
            },
            hooks: {},
            expressions: {
                hide: 'model.list_stages !== "Groups"',
            }
        },
        {
            fieldGroupClassName: 'row',
            fieldGroup: [
                {
                    className: 'col-md-4',
                    key: 'begin_date',
                    type: 'custom-date',
                    props: {
                        label: this._translateService.instant('Date'),
                        placeholder: this._translateService.instant('dd/mm/yyyy'),
                        required: true
                    },
                    defaultValue: moment().format('YYYY-MM-DD')
                },
                {
                    className: 'col-md-4',
                    key: 'begin_time',
                    type: 'input',
                    props: {
                        label: this._translateService.instant('First Match Start Time'),
                        placeholder: this._translateService.instant('Select start time'),
                        required: true,
                        type: 'time'
                    },
                    defaultValue: moment().format('HH:mm')
                },
                {
                    className: 'col-md-4',
                    key: 'end_time',
                    type: 'input',
                    props: {
                        label: this._translateService.instant('Latest End Time'),
                        placeholder: this._translateService.instant('Select end time'),
                        required: true,
                        type: 'time'
                    },
                    defaultValue: moment().format('HH:mm'),
                    validators: {
                        endTimeValidator: {
                            expression: (control) => {
                                const form = control.parent;
                                if (!form) return true;
                                const beginTime = form.get('begin_time').value;
                                const endTime = control.value;
                                return !beginTime || !endTime || endTime > beginTime;
                            },
                            message: this._translateService.instant('End time must be later than start time')
                        }
                    }
                },
            ]
        },
        {
            fieldGroupClassName: 'row',
            fieldGroup: [

                {
                    className: 'col-md-6',
                    key: 'match_duration',
                    type: 'input',
                    props: {
                        label: this._translateService.instant('Match Duration (minutes)'),
                        placeholder: this._translateService.instant('Enter match duration'),
                        required: true,
                        type: 'number',
                        min: 1,
                        step: 1,
                        pattern: '[0-9]*'
                    },
                    defaultValue: 25,
                    validation: {
                        messages: {
                            min: this._translateService.instant('Match duration must be at least 1 minute.'),
                            pattern: this._translateService.instant('Match duration must be an integer number.')
                        }
                    }
                },
                {
                    className: 'col-md-6',
                    key: 'break_duration',
                    type: 'input',
                    props: {
                        label: this._translateService.instant('Break Duration (minutes)'),
                        placeholder: this._translateService.instant('Enter break duration between matches'),
                        required: true,
                        type: 'number',
                        min: 0,
                        step: 1,
                        pattern: '[0-9]*'
                    },
                    defaultValue: 0,
                    validation: {
                        messages: {
                            min: this._translateService.instant('Break duration must be at least 0 minutes.'),
                            pattern: this._translateService.instant('Break duration must be an integer number.')
                        }
                    }
                },
            ]
        },
        {
            key: 'list_location_ids',
            type: 'ng-select',
            props: {
                multiple: true,
                required: true,
                hideOnMultiple: true,
                defaultValue: [],
                label: 'Locations',
                placeholder: 'Select locations for scheduling',
                options: []
            },
            hooks: {},
            validation: {
                messages: {
                    required: this._translateService.instant('Please select at least one location.')
                }
            },
        },
        {
            fieldGroupClassName: "row",
            fieldGroup: [
                {
                    key: 'nums_of_referees',
                    type: 'input',
                    className: 'col-md-4',
                    defaultValue: 0,
                    props: {
                        label: this._translateService.instant('Referees per Match'),
                        placeholder: this._translateService.instant('Enter number of referees'),
                        required: true,
                        type: 'number',
                        min: 0,
                    },
                    validation: {
                        messages: {
                            min: this._translateService.instant('Number of referees must be at least 0.')
                        }
                    }
                },
                {
                    key: 'list_referee_ids',
                    type: 'ng-select',
                    className: 'col-md-8',
                    props: {
                        multiple: true,
                        hideOnMultiple: true,
                        defaultValue: [],
                        label: this._translateService.instant('Referees'),
                        placeholder: this._translateService.instant('Select referees'),
                        options: []
                    },
                    expressions: {
                        "props.disabled": 'model.nums_of_referees === 0',
                        "props.required": 'model.nums_of_referees > 0'
                    },
                    validation: {
                        messages: {
                            required: this._translateService.instant('Please select at least one referee.')
                        }
                    }

                }
            ]
        },
        {
            key: 'tournament_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'stage_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        }
    ];

    @Output() onSubmit = new EventEmitter();

    constructor(
        private _modalService: NgbModal,
        private _translateService: TranslateService,
        private _tournamentService: TournamentService,
        private _locationService: LocationService,
        private _autoSchedule: AutoScheduleService,
        private _loadingService: LoadingService,
        private _seasonService: SeasonService,
    ) {

    }

    ngOnInit() {
        this._loadingService.show();

        this.setupScheduleFields[this.setupScheduleFields.length - 2].defaultValue = this.tournamentId;

        const observables = [
            this._tournamentService.getGroupInTournament(this.tournamentId),
            this._locationService.getAllLocations(),
            this._seasonService.getListSeasonReferees(this.seasonId),
            this._tournamentService.getStagesInTournament(this.tournamentId)
        ];

        forkJoin(observables).subscribe({
            next: ([groupRes, locationRes, refereeRes, stagesRes]) => {
                this.listGroups = groupRes.data;
                this.setupScheduleFields[1].props.options = groupRes.data;

                this.setupScheduleFields[4].props.options = locationRes['data'].map((location) => ({
                    label: location.name,
                    value: location.id
                }));

                this.setupScheduleFields[5].fieldGroup[1].props.options = refereeRes['data'].map((referee) => {
                    this.refereeIds.push(referee.id);
                    return {
                        label: referee.user
                            ? `${referee.user.first_name} ${referee.user.last_name}`
                            : referee.referee_name,
                        value: referee.id
                    }
                });

                this.listStages = stagesRes;

                if (this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {
                    this.setupScheduleFields[0].props.options = [
                        {
                            label: 'All',
                            value: 'All',
                        },
                        ...stagesRes.map((item) => ({
                            label: item.name,
                            value: item.name
                        }))
                    ];
                } else {
                    this.setupScheduleFields[0].props.options = [
                        ...stagesRes.map((item) => ({
                            label: item.name,
                            value: item.name
                        }))
                    ];
                }
            },
            complete: () => {
                this._loadingService.dismiss();
            }
        });
    }

    onSubmitSetup(model) {
        // handle check model is all valids
        if (this.setupScheduleForm.invalid) {
            return;
        }

        if (model.list_stages === AppConfig.TOURNAMENT_TYPES.groups && !model.list_group_names) {
            model.list_group_names = this.listGroups;
        }

        this._autoSchedule.scheduleTournament(model).subscribe((res) => {
            console.log('res', res);
            this.onSubmit.emit(res);
            this._modalService.dismissAll();
        }, (error) => {
            Swal.fire({
                title: 'Cannot Auto Schedule!',
                text: error.message,
                icon: 'warning',
                confirmButtonText: 'Ok'
            });
        });
    }


    closeModal() {
        this.setupScheduleModel = {};
        this._modalService.dismissAll();
    }

    clearForm() {
        this.setupScheduleForm.reset();
    }

}
