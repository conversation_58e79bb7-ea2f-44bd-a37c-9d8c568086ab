{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from '../../../../../environments/environment';\nimport { EventEmitter } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { AppConfig } from 'app/app-config';\nimport { DataTableDirective } from 'angular-datatables';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"app/services/stage.service\";\nimport * as i6 from \"../../../../services/user.service\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i9 from \"ngx-toastr\";\nimport * as i10 from \"app/services/export.service\";\nimport * as i11 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i14 from \"angular-datatables\";\nimport * as i15 from \"../../../../components/editor-sidebar/editor-sidebar.component\";\nimport * as i16 from \"./modal-assign-referees/modal-assign-referees.component\";\nconst _c0 = [\"modalAssignReferee\"];\nfunction StageMatchesComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 9, 10)(2, \"li\", 11)(3, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function StageMatchesComponent_ul_4_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.filterFriendlyMatches(false));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 11)(7, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function StageMatchesComponent_ul_4_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.filterFriendlyMatches(true));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"All Matches\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 4, \"Friendly Matches\"));\n  }\n}\nfunction StageMatchesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-assign-referees\", 13);\n    i0.ɵɵlistener(\"onSubmit\", function StageMatchesComponent_ng_template_8_Template_app_modal_assign_referees_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSubmitAssignReferee($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"isMultipleAssign\", ctx_r2.isMultipleAssign)(\"selectedIds\", ctx_r2.selectedIds)(\"listReferees\", ctx_r2.listReferees)(\"assignRefereeForm\", ctx_r2.assignRefereeForm)(\"assignRefereeFields\", ctx_r2.assignRefereeFields)(\"assignRefereeModel\", ctx_r2.assignRefereeModel);\n  }\n}\nexport class StageMatchesComponent {\n  // setup referee\n  constructor(_http, _route, _translateService, _commonsService, _stageService, _userService, _loadingService, _coreSidebarService, _toastr, _exportService, _modalService) {\n    this._http = _http;\n    this._route = _route;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._stageService = _stageService;\n    this._userService = _userService;\n    this._loadingService = _loadingService;\n    this._coreSidebarService = _coreSidebarService;\n    this._toastr = _toastr;\n    this._exportService = _exportService;\n    this._modalService = _modalService;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.AppConfig = AppConfig;\n    this.hasMatch = false;\n    this.isComplete = false;\n    this.onUpdateScore = new EventEmitter();\n    this.onDataChange = new EventEmitter();\n    this.listReferees = [];\n    this.friendlyMatchesActive = false;\n    this.fields_subject = new Subject();\n    this.cancelOptions = [];\n    this.paramsToPost = {};\n    this.is_score_updated = false;\n    this.table_name = 'matches-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Add match'),\n        edit: this._translateService.instant('Edit match'),\n        remove: this._translateService.instant('Remove match')\n      },\n      url: `${environment.apiUrl}/stage-matches/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.location = {\n      options: [],\n      selected: null\n    };\n    this.teams = {\n      options: [],\n      selected: null\n    };\n    this.roundLevelOpts = [];\n    this.editMatch = [{\n      key: 'date',\n      type: 'custom-date',\n      props: {\n        label: this._translateService.instant('Date'),\n        placeholder: this._translateService.instant('dd/mm/yyyy'),\n        // required: true,\n        max: '2100-12-31'\n      },\n      expressions: {\n        'props.required': '(model.hasOwnProperty(\"start_time_short\") && model.start_time_short!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\n      }\n    }, {\n      key: 'start_time_short',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Start time'),\n        placeholder: this._translateService.instant('Enter start time of match'),\n        // required: true,\n        type: 'time'\n      },\n      expressions: {\n        'props.required': '(model.hasOwnProperty(\"date\") && model.date!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\n      }\n    }, {\n      key: 'end_time_short',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('End time'),\n        placeholder: this._translateService.instant('Enter end time of match'),\n        // required: true,\n        type: 'time'\n      }\n    }, {\n      key: 'location_id',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Location'),\n        placeholder: this._translateService.instant('Select location'),\n        // required: true,\n        options: this.location.options\n      }\n    }, {\n      key: 'home_team_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Home team'),\n        placeholder: this._translateService.instant('Select home team'),\n        options: []\n      }\n    }, {\n      key: 'away_team_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Away team'),\n        placeholder: this._translateService.instant('Select away team'),\n        options: []\n      }\n    }];\n    this.updateScore = [];\n    this.cancelMatch = [{\n      key: 'status',\n      type: 'radio',\n      props: {\n        label: this._translateService.instant('Cancel type'),\n        required: true,\n        options: this.cancelOptions\n      }\n    }, {\n      key: 'description',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Reason'),\n        placeholder: this._translateService.instant('Enter reason')\n      }\n    }];\n    this.updateRankMatch = [{\n      key: 'home_label_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Home label'),\n        placeholder: this._translateService.instant('Select home label'),\n        options: []\n      }\n    }, {\n      key: 'away_label_id',\n      type: 'select',\n      props: {\n        hideOnMultiple: true,\n        label: this._translateService.instant('Away label'),\n        placeholder: this._translateService.instant('Select away label'),\n        options: []\n      }\n    }];\n    this.fields = this.editMatch;\n    this.rank_fields = this.updateRankMatch;\n    this.isMultipleAssign = true;\n    this.selectedIds = [];\n    this.assignRefereeForm = new FormGroup({});\n    this.assignRefereeModel = {};\n    this.assignRefereeFields = [{\n      key: 'list_referees',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: this._translateService.instant('Match Referee'),\n        placeholder: this._translateService.instant('Select match referees'),\n        options: []\n      },\n      hooks: {}\n    }];\n  }\n  ngOnInit() {\n    this.loadInitSettings();\n    this.setUp();\n    this.buildTable();\n    this.getListReferee();\n  }\n  loadInitSettings() {\n    const settings = localStorage.getItem('initSettings');\n    if (settings) {\n      this.initSettings = JSON.parse(settings);\n      console.log('initSettings:', this.initSettings);\n    } else {\n      console.log('No initSettings found in localStorage');\n    }\n  }\n  setUp() {\n    AppConfig.CANCEL_MATCH_TYPES.forEach(type => {\n      this.cancelOptions.push({\n        value: type,\n        label: this._translateService.instant(type)\n      });\n    });\n    this.editMatch.push({\n      key: 'stage_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: this.stage.id\n    });\n    this.updateScore = [{\n      key: 'type',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: this.stage.type\n    }, {\n      key: 'home_score',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Home score'),\n        required: true,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'away_score',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Away score'),\n        required: true,\n        type: 'number',\n        min: 0,\n        max: 999,\n        step: 1\n      },\n      defaultValue: 0\n    }, {\n      key: 'home_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Home penalty score'),\n        // required: true,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      },\n      defaultValue: 0\n    }, {\n      key: 'away_penalty',\n      type: 'core-touchspin',\n      props: {\n        label: this._translateService.instant('Away penalty score'),\n        // required: true,\n        type: 'number',\n        min: 0,\n        max: 100,\n        step: 1\n      },\n      expressions: {\n        hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\n      },\n      defaultValue: 0\n    }];\n    this.contentHeader = {\n      headerTitle: this._translateService.instant('League Reports'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._translateService.instant('Leagues'),\n          isLink: false\n        }, {\n          name: this._translateService.instant('League Reports'),\n          isLink: false\n        }]\n      }\n    };\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league || this.stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\n      // get round level options in stage\n      for (let i = 1; i <= this.stage.no_encounters; i++) {\n        this.roundLevelOpts.push({\n          value: `${i}`,\n          label: `${this._translateService.instant('Round')} ${i}`\n        });\n      }\n    }\n    if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      // add match type at beginning of editMatch\n      this.editMatch.splice(0, 0, {\n        key: 'match_type',\n        type: 'select',\n        props: {\n          label: this._translateService.instant('Match type'),\n          required: true,\n          options: [{\n            value: 1,\n            label: this._translateService.instant('League Match')\n          }, {\n            value: 2,\n            label: this._translateService.instant('Friendly Match')\n          }]\n        }\n      });\n    }\n    if (this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n      // find round level index in editMatch\n      let roundLevelIndex = this.editMatch.findIndex(field => field.key === 'round_level');\n      // remove round level in editMatch\n      if (roundLevelIndex > -1) {\n        this.editMatch.splice(roundLevelIndex, 1);\n      }\n      // add round name at beginning of editMatch\n      this.editMatch.splice(0, 0, {\n        key: 'round_name',\n        type: 'input',\n        props: {\n          hideOnMultiple: true,\n          label: this._translateService.instant('Match name'),\n          placeholder: this._translateService.instant('Enter round name')\n        }\n      });\n    }\n  }\n  buildTable() {\n    var _this = this;\n    let btns = [{\n      text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant('Add')}`,\n      action: (e, dt, node, config) => {\n        // check if stage type is league\n        if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not add match in this stage'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('create', 'editMatch');\n      }\n    }, {\n      attr: {\n        id: 'auto-generate-btn'\n      },\n      text: `<i class=\"fa-solid fa-wand-magic-sparkles\"></i> ${this._translateService.instant('Auto Generate')}`,\n      action: (e, dt, node, config) => {\n        this.autoGenerateMatches();\n      }\n    }, {\n      text: `<i class=\"feather icon-edit\"></i> ${this._translateService.instant('Update Score')}`,\n      action: (e, dt, node, config) => {\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        let row = selectedRows[0];\n        let validUpdate = false;\n        if (this.checkValidateUpdateScore(row)) {\n          validUpdate = true;\n        }\n        if (!validUpdate) {\n          Swal.fire({\n            title: this._translateService.instant('Cannot update score'),\n            text: this._translateService.instant('Please update the date, time and location of the match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n        } else {\n          this.editor('edit', 'updateScore');\n        }\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-rotate-ccw\"></i> ${this._translateService.instant('Reset Score')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // get stage_match id\n        let ids = [];\n        selectedRows.map(row => ids.push(row.id));\n        // confirm reset score\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          text: this._translateService.instant('Are you sure you want to reset score this match(s)?'),\n          reverseButtons: true,\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: this._translateService.instant('No'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary ml-1',\n            cancelButton: 'btn btn-outline-primary'\n          }\n        }).then(result => {\n          if (result.value) {\n            this.resetScore(ids);\n          }\n        });\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-flag\"></i>${this._translateService.instant('Assign Referee')}`,\n      action: () => this.openModal(),\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-edit\"></i>${this._translateService.instant('Edit')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        if (selectedRows.length > 1) {\n          // hide round level\n          let roundLevelIndex = this.editMatch.findIndex(x => x.key == 'round_level');\n          if (roundLevelIndex > -1) {\n            this.editMatch.splice(roundLevelIndex, 1);\n          }\n          let roundNameIndex = this.editMatch.findIndex(x => x.key == 'round_name');\n          if (roundNameIndex > -1) {\n            this.editMatch.splice(roundNameIndex, 1);\n          }\n        } else {\n          // show round level\n          let roundLevelIndex = this.editMatch.findIndex(x => x.key == 'round_level');\n          // if not found\n          if (roundLevelIndex == -1 && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n            this.editMatch.splice(3, 0, {\n              key: 'round_level',\n              type: 'select',\n              props: {\n                label: this._translateService.instant('Round level'),\n                placeholder: this._translateService.instant('Select round level'),\n                // required: true,\n                options: this.roundLevelOpts\n              }\n            });\n          }\n          if (this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n            let roundNameIndex = this.editMatch.findIndex(x => x.key == 'round_name');\n            if (roundNameIndex == -1) {\n              this.editMatch.splice(0, 0, {\n                key: 'round_name',\n                type: 'input',\n                props: {\n                  label: this._translateService.instant('Match name'),\n                  placeholder: this._translateService.instant('Enter round name')\n                }\n              });\n            }\n          }\n        }\n        this.editor('edit', 'editMatch');\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant('Add replace match')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        let row = selectedRows[0];\n        let hasCancel = false;\n        if (this.checkRowIsCancelled(row)) {\n          hasCancel = true;\n        }\n        if (!hasCancel) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not add replace match for this match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('create', 'addReplaceMatch', row);\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant('Swap teams')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        // check if home_team_id or away_team_id is null\n        if (selectedRows[0].status !== 'pass' && (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id)) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('You can not swap teams for this match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        } else {\n          // confirm swap teams\n          Swal.fire({\n            title: this._translateService.instant('Are you sure?'),\n            text: this._translateService.instant('Are you sure you want to swap teams in this match?'),\n            icon: 'warning',\n            reverseButtons: true,\n            showCancelButton: true,\n            confirmButtonText: this._translateService.instant('Yes'),\n            cancelButtonText: this._translateService.instant('No'),\n            buttonsStyling: false,\n            customClass: {\n              confirmButton: 'btn btn-primary ml-1',\n              cancelButton: 'btn btn-outline-primary'\n            }\n          }).then(result => {\n            if (result.value) {\n              this.swapTeams(selectedRows[0]);\n            }\n          });\n        }\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"fa-solid fa-ban\"></i> ${this._translateService.instant('Cancel')}`,\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows has status is not can play\n        let hasCancel = false;\n        selectedRows.map(row => {\n          if (this.checkRowIsCancelled(row)) {\n            hasCancel = true;\n          }\n        });\n        if (hasCancel) {\n          Swal.fire({\n            title: this._translateService.instant('Warning'),\n            text: this._translateService.instant('You can not cancel match that has been cancelled'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('OK'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        // confirm cancel match\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          text: this._translateService.instant('Are you sure you want to cancel this match(s)?'),\n          reverseButtons: true,\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: this._translateService.instant('No'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary ml-1',\n            cancelButton: 'btn btn-outline-primary'\n          }\n        }).then(result => {\n          if (result.value) {\n            this.editor('edit', 'cancelMatch');\n          }\n        });\n      },\n      extend: 'selected'\n    }, {\n      text: `<i class=\"feather icon-trash\"></i> ${this._translateService.instant('Delete')}`,\n      extend: 'selected',\n      action: (e, dt, node, config) => {\n        // get selected rows\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // get ids\n        let ids = [];\n        selectedRows.map(row => {\n          ids.push(row.id);\n        });\n        // confirm delete\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          text: this._translateService.instant('You will not be able to recover this!'),\n          reverseButtons: true,\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: this._translateService.instant('No'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary ml-1',\n            cancelButton: 'btn btn-outline-primary'\n          }\n        }).then(result => {\n          if (result.value) {\n            // delete\n            this._loadingService.show();\n            this._stageService.deleteMatchesInStage(ids, this.stage.id).toPromise().then(resp => {\n              this._toastr.success(this._translateService.instant('Deleted successfully'));\n              dt.ajax.reload();\n              this.onDataChange.emit(resp);\n            });\n          }\n        });\n      }\n    }, {\n      text: `<i class=\"fas fa-file-export mr-1\"></i> ${this._translateService.instant('Export')}`,\n      extend: 'csv',\n      action: function () {\n        var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n          const data = dt.buttons.exportData();\n          yield _this._exportService.exportCsv(data, 'Matches.csv');\n        });\n        return function action(_x, _x2, _x3, _x4) {\n          return _ref.apply(this, arguments);\n        };\n      }()\n    }, {\n      text: `<i class=\"fa-solid fa-code\"></i> ${this._translateService.instant('Define Team')}`,\n      action: (e, dt, node, config) => {\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        // check if selected rows > 1\n        if (selectedRows.length > 1) {\n          Swal.fire({\n            title: this._translateService.instant('Notification'),\n            text: this._translateService.instant('Please select only one match'),\n            icon: 'warning',\n            confirmButtonText: this._translateService.instant('Ok'),\n            customClass: {\n              confirmButton: 'btn btn-primary'\n            }\n          });\n          return;\n        }\n        this.editor('edit', 'updateRankMatch');\n      },\n      extend: 'selected'\n    }, {\n      text: this._translateService.instant('Columns'),\n      extend: 'colvis'\n    }];\n    // if stage type is league\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\n      // remove first button\n      btns.splice(0, 1);\n    }\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.knockout || this.tournament.type_knockout === AppConfig.KNOCKOUT_TYPES.type4) {\n      btns.splice(-2, 1);\n    }\n    if (this.tournament.type_knockout != AppConfig.KNOCKOUT_TYPES.type4 && this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout) {\n      btns.splice(0, 1);\n      btns.splice(-4, 1);\n    }\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: window.innerWidth > 768 ? 'os' : 'multi',\n      // serverSide: true,\n      rowId: 'id',\n      rowGroup: {\n        dataSrc: 'group_round'\n      },\n      order: [[2, 'asc']],\n      ajax: (dataTablesParameters, callback) => {\n        // add season id\n        this._http.post(`${environment.apiUrl}/stage-matches/all-in-stage/${this.stage.id}`, dataTablesParameters).subscribe(resp => {\n          // find fields has key location_id and set options\n          this.fields.forEach(field => {\n            if (field.key === 'location_id') {\n              field.props.options = resp.options.location;\n            }\n            if (field.key === 'home_team_id') {\n              field.props.options = resp.options.teams;\n            }\n            if (field.key === 'away_team_id') {\n              field.props.options = resp.options.teams;\n            }\n          });\n          this.rank_fields.forEach(field => {\n            if (field.key === 'home_label_id') {\n              field.props.options = resp.options.rank_label;\n            }\n            if (field.key === 'away_label_id') {\n              field.props.options = resp.options.rank_label;\n            }\n          });\n          this.filterFriendlyMatches(this.friendlyMatchesActive);\n          this.hasMatch = resp.data.length > 0;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      initComplete: () => {\n        this.isComplete = true;\n      },\n      responsive: false,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -7\n      }, {\n        responsivePriority: 1,\n        targets: -6\n      }, {\n        responsivePriority: 1,\n        targets: -5\n      }, {\n        responsivePriority: 1,\n        targets: -4\n      }, {\n        responsivePriority: 1,\n        targets: -3\n      }, {\n        responsivePriority: 1,\n        targets: -2\n      }],\n      columns: [{\n        data: 'order',\n        visible: false\n      }, {\n        data: 'match_type',\n        visible: false\n      }, {\n        data: 'group_round',\n        visible: false\n      }, {\n        title: this._translateService.instant('No'),\n        data: 'match_number',\n        visible: this.stage.type !== AppConfig.TOURNAMENT_TYPES.league,\n        render: function (data, type, row, meta) {\n          return data ? `${data}` : `${meta.row + 1}`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'date',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          let content = '';\n          if (!data || data === 'TBD') {\n            content = 'TBD';\n          } else {\n            // format to HH:mm from ISO 8601\n            content = moment(data).format('YYYY-MM-DD');\n          }\n          return `<p style=\"margin:0; min-width: max-content\">${content}</p>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time_short',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          if (!data || row.start_time_short == 'TBD' || !row.start_time_short) {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time_short',\n        orderable: false,\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          if (!data || row.end_time_short == 'TBD' || !row.end_time_short) {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location_name'\n      }, {\n        title: this._translateService.instant('Home team'),\n        data: 'home_team_name',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (data === 'TBD') {\n            if (row.home_text) {\n              return row.home_text;\n            } else if (row.home_label && row.home_label.label) {\n              return row.home_label.label;\n            }\n          }\n          return data;\n        }\n      }, {\n        data: null,\n        className: 'text-center p-0 font-weight-bolder',\n        render: function (data, type, row) {\n          return `VS`;\n        },\n        orderable: false\n      }, {\n        title: this._translateService.instant('Away team'),\n        data: 'away_team_name',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (data === 'TBD') {\n            if (row.away_text) {\n              return row.away_text;\n            } else if (row.away_label && row.away_label.label) {\n              return row.away_label.label;\n            }\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (row.home_penalty != null) {\n            return `${data}<br> ( ${row.home_penalty} )`;\n          }\n          return data;\n        }\n      }, {\n        data: null,\n        className: 'text-center p-0',\n        orderable: false,\n        render: function (data, type, row) {\n          return ` - `;\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center p-1',\n        orderable: false,\n        render: function (data, type, row) {\n          if (row.away_penalty != null) {\n            return `${data}<br> ( ${row.away_penalty} )`;\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Referees'),\n        visible: false,\n        data: 'referees',\n        render: data => {\n          return `<div class=\"d-flex flex-column gap-1\">\n              ${data.map(item => {\n            const refereeName = item.user ? `${item.user.first_name} ${item.user.last_name}` : item.referee_name;\n            return `<p style=\"min-width: max-content; margin: 0\">${refereeName}</p>`;\n          }).join('')}\n            </div>`;\n        }\n      }, {\n        title: this._translateService.instant('Status'),\n        data: 'status',\n        render: (data, type, row) => {\n          const now = moment();\n          const currentTime = now.format('HH:mm');\n          const today = now.format('YYYY-MM-DD');\n          if (!data) {\n            // check if match time is over\n            if (row.end_time_short == 'TBD') {\n              return `<span class=\"badge badge-secondary text-capitalize\">TBD</span>`;\n            } else {\n              if (row.date == today && row.end_time_short < currentTime || row.date < today) {\n                return `<span class=\"badge badge-success text-capitalize\">${this._translateService.instant('Finished')}</span>`;\n              } else if (row.date == today && row.start_time_short <= currentTime && row.end_time_short >= currentTime) {\n                return `<span class=\"badge badge-warning text-capitalize\">${this._translateService.instant('In Progress')}</span>`;\n              } else if (row.date > today || row.date == today && row.start_time_short > currentTime) {\n                return `<span class=\"badge badge-info text-capitalize\">${this._translateService.instant('Upcoming')}</span>`;\n              }\n            }\n          } else {\n            if (AppConfig.CANCEL_MATCH_TYPES.includes(data)) {\n              return `<span class=\"badge badge-danger text-capitalize\">${data}</span>`;\n            } else {\n              return `<span class=\"badge badge-secondary text-capitalize\">${data}</span>`;\n            }\n          }\n        },\n        className: 'text-center p-1',\n        orderable: false\n      }],\n      lengthMenu: [[25, 50, 100, -1], [25, 50, 100, 'All']],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: btns\n      }\n    };\n    switch (this.stage.type) {\n      case AppConfig.TOURNAMENT_TYPES.knockout:\n        this.dtOptions.order = [[0, 'asc']];\n        // add rowGroup\n        this.dtOptions.rowGroup = {\n          dataSrc: 'round_name'\n        };\n        // insert round_name column at index 6\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Name'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data) {\n            return `<p style=\"margin:0; min-width: max-content\">${data}</p>`;\n          }\n        });\n        break;\n      case AppConfig.TOURNAMENT_TYPES.groups:\n        this.dtOptions.order = [[2, 'asc']];\n        // insert round_name column at index 5\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data, type, row) {\n            // split round_name by - and get the last item\n            if (!data) return '';\n            let round_name = data.split('-').pop();\n            return `<p style=\"margin:0; min-width: max-content\">${round_name}</p>`;\n          }\n        });\n        break;\n      case AppConfig.TOURNAMENT_TYPES.league:\n        // clear rowGroup\n        this.dtOptions.rowGroup = null;\n        this.dtOptions.columns.splice(7, 0, {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          className: 'text-center',\n          render: function (data) {\n            console.log('data', data);\n            return `<p style=\"margin:0; min-width: max-content\">${data ?? ''}</p>`;\n          }\n        });\n        // order by round_level\n        this.dtOptions.order = [[5, 'asc']];\n        break;\n      default:\n        break;\n    }\n  }\n  addOrRemoveRoundLevel(is_friendly) {\n    const round_level = {\n      key: 'round_level',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Round level'),\n        placeholder: this._translateService.instant('Select round level'),\n        // required: true,\n        options: this.roundLevelOpts\n      }\n    };\n    if (is_friendly) {\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\n        // remove round_level from editMatch\n        const round_level_index = this.editMatch.findIndex(item => item.key === 'round_level');\n        // check if round_level exist\n        if (round_level_index > -1) {\n          this.editMatch.splice(round_level_index, 1);\n        }\n      }\n    } else {\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\n        const round_level_index = this.editMatch.findIndex(item => item.key === 'round_level');\n        // check if round_level not exist\n        if (round_level_index < 0) {\n          this.editMatch.splice(2, 0, {\n            ...round_level\n          });\n        }\n      }\n    }\n  }\n  filterFriendlyMatches(active) {\n    this.friendlyMatchesActive = active;\n    this.addOrRemoveRoundLevel(active);\n    // deselect all rows\n    this.dtElement.dtInstance.then(dtInstance => {\n      // round level backup\n      dtInstance.rows().deselect();\n      if (!active) {\n        // show column round name\n        dtInstance.column(5).visible(true);\n        dtInstance.column(1).search(1).draw();\n        // disable button auto generate\n        let btns = dtInstance.button('#auto-generate-btn');\n        console.log(btns);\n        if (btns) {\n          btns.enable();\n        }\n      } else {\n        // hide column round name\n        dtInstance.column(5).visible(true);\n        dtInstance.column(1).search(2).draw();\n        // enable button auto generate\n        let btns = dtInstance.button('#auto-generate-btn');\n        console.log(btns);\n        if (btns) {\n          btns.disable();\n        }\n      }\n    });\n  }\n  checkRowIsCancelled(row) {\n    let hasCancel = false;\n    // if row has status in AppConfig.CANCEL_MATCH_TYPES\n    if (AppConfig.CANCEL_MATCH_TYPES.includes(row.status)) {\n      hasCancel = true;\n    }\n    return hasCancel;\n  }\n  _generateMatches() {\n    this._stageService.autoGenerateMatches(this.stage.id).subscribe(resp => {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    }, error => {\n      if (error.warning) {\n        Swal.fire({\n          title: this._translateService.instant('Cannot Auto Generate'),\n          html: error.warning.replace(/\\n/g, '<br>'),\n          icon: 'warning',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n        return;\n      } else {\n        Swal.fire({\n          title: this._translateService.instant('Error'),\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n      }\n    });\n  }\n  autoGenerateMatches() {\n    console.log('auto generate matches', this.hasMatch);\n    // check if matches not null\n    if (this.hasMatch) {\n      this._loadingService.show();\n      Swal.fire({\n        title: `${this._translateService.instant('Are you sure?')}`,\n        html: `\n                <p>${this._translateService.instant('All current matches will be deleted unless they contain information')}.</p>\n                <div class=\"swal2-checkbox-container\" style=\"display:flex;justify-content:center;gap: 10px;\">\n                    <input type=\"checkbox\" id=\"confirmCheckbox\" >\n                    <label for=\"confirmCheckbox\" class=\"swal2-label\">${this._translateService.instant('I confirm that I want to generate all current matches')}</label>\n                    <div class=\"text-danger swal2-label\" id=\"swal2-validation-message\" style=\"display: none;\"></div>\n                </div>\n            `,\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this._translateService.instant('Yes'),\n        cancelButtonText: this._translateService.instant('No')\n      }).then(result => {\n        if (result.isConfirmed) {\n          this._generateMatches();\n        } else {\n          this._loadingService.dismiss();\n        }\n      });\n    } else {\n      if (this.isComplete) {\n        this._generateMatches();\n      }\n    }\n  }\n  resetScore(stage_match_id) {\n    console.log(`reset score for ${stage_match_id}`);\n    this._loadingService.show();\n    this._stageService.resetScore(stage_match_id).subscribe(resp => {\n      console.log(resp);\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    }, error => {\n      Swal.fire({\n        title: this._translateService.instant('Error'),\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      });\n    });\n  }\n  swapTeams(stage_match) {\n    this._loadingService.show();\n    this._stageService.swapTeams(stage_match).subscribe(resp => {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.onDataChange.emit(resp);\n      });\n    });\n  }\n  onSuccess($event) {\n    if (this.is_score_updated) {\n      this.onUpdateScore.emit($event);\n    }\n    this.onDataChange.emit($event);\n  }\n  checkValidateUpdateScore(row) {\n    return !(!row.date || !row.start_time || !row.end_time || !row.location);\n  }\n  editor(action, fields, row) {\n    this.params.use_data = false;\n    this.paramsToPost = {\n      stage_type: this.stage.type,\n      type_action: fields\n    };\n    // check has row with status is pass in selected rows\n    this.dtElement.dtInstance.then(dtInstance => {\n      let selectedRows = dtInstance.rows({\n        selected: true\n      }).data();\n      switch (fields) {\n        case 'editMatch':\n          this.params.title.edit = this._translateService.instant('Edit match');\n          this.fields = [...this.editMatch, {\n            key: 'match_status',\n            type: 'input',\n            props: {\n              type: 'hidden'\n            },\n            defaultValue: selectedRows[0]?.status\n          }].map(item => ({\n            ...item\n          }));\n          console.log('fields', this.fields);\n          this.is_score_updated = false;\n          this.fields.forEach(item => {\n            if (item.key === 'referee') {\n              item.value = selectedRows[0]?.referees?.map(r => r.id) || [];\n            }\n          });\n          break;\n        case 'updateScore':\n          this.params.title.edit = this._translateService.instant('Update Score');\n          this.fields = this.updateScore;\n          this.is_score_updated = true;\n          break;\n        case 'cancelMatch':\n          this.params.title.edit = this._translateService.instant('Cancel Match');\n          this.fields = this.cancelMatch;\n          this.is_score_updated = false;\n          break;\n        case 'addReplaceMatch':\n          console.log(row);\n          let paramsPost = {\n            'data[0][round_name]': row.round_name,\n            'data[0][round_level]': row.round_level,\n            'data[0][match_id]': row.id,\n            'data[0][match_number]': row.match_number || 0\n          };\n          // merge paramsToPost and paramsPost\n          this.paramsToPost = {\n            ...this.paramsToPost,\n            ...paramsPost\n          };\n          this.params.title.edit = this._translateService.instant('Add Replace Match');\n          this.fields = this.editMatch;\n          this.params.use_data = true;\n          break;\n        case 'updateRankMatch':\n          this.params.title.edit = this._translateService.instant('Edit Rank');\n          this.fields = this.updateRankMatch;\n          this.is_score_updated = false;\n          break;\n        default:\n          this.params.title.edit = this._translateService.instant('Add Match');\n          this.fields = this.editMatch.map(item => {\n            if (item.key === 'referee') {\n              item['defaultValue'] = [];\n              item['value'] = [];\n            }\n            return item;\n          });\n          this.is_score_updated = false;\n          break;\n      }\n      this.fields_subject.next(this.fields);\n      this.params.action = action;\n      this.params.row = row ? row : null;\n      this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n    });\n  }\n  onCloseSidebar(event) {\n    this.editMatch.forEach(e => {\n      if (e.key === 'referee') {\n        e['defaultValue'] = [];\n        e['value'] = [];\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  openModal() {\n    this.dtElement.dtInstance.then(dtInstance => {\n      let selectedRows = dtInstance.rows({\n        selected: true\n      }).data();\n      this.selectedIds = [];\n      selectedRows.map(row => {\n        return this.selectedIds.push(row.id);\n      });\n      if (selectedRows.length > 1) {\n        this.isMultipleAssign = true;\n        this.assignRefereeModel = {\n          ...this.assignRefereeModel,\n          list_referees: []\n        };\n      } else {\n        this.isMultipleAssign = false;\n        this.assignRefereeModel = {\n          ...this.assignRefereeModel,\n          list_referees: selectedRows[0]?.referees?.map(r => r.id) || []\n        };\n      }\n      this._modalService.open(this.modalAssignReferee, {\n        centered: true,\n        size: 'lg',\n        beforeDismiss: () => {\n          this.assignRefereeModel = {};\n          this.assignRefereeFields.forEach(item => {\n            if (item.key === 'list_referees') {\n              item['defaultValue'] = [];\n            }\n          });\n          return true;\n        }\n      });\n    });\n  }\n  getListReferee() {\n    this._stageService.getListRefereesByStageId(this.stage.id).subscribe(response => {\n      this.listReferees = response['data'];\n      this.assignRefereeFields.forEach(item => {\n        if (item.key === 'list_referees') {\n          item.props.options = response['data'].map(referee => {\n            console.log(referee);\n            return {\n              label: referee.user ? `${referee.user.first_name} ${referee.user.last_name}` : referee.referee_name,\n              value: referee.id\n            };\n          });\n        }\n      });\n    });\n  }\n  onSubmitAssignReferee(event) {\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  autoschedule() {\n    this._stageService.autoSchedule(this.stage.id).subscribe(response => {\n      console.log(response);\n    });\n  }\n  static #_ = this.ɵfac = function StageMatchesComponent_Factory(t) {\n    return new (t || StageMatchesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.StageService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.CoreSidebarService), i0.ɵɵdirectiveInject(i9.ToastrService), i0.ɵɵdirectiveInject(i10.ExportService), i0.ɵɵdirectiveInject(i11.NgbModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageMatchesComponent,\n    selectors: [[\"app-stage-matches\"]],\n    viewQuery: function StageMatchesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalAssignReferee = _t.first);\n      }\n    },\n    inputs: {\n      stage: \"stage\",\n      tournament: \"tournament\"\n    },\n    outputs: {\n      onUpdateScore: \"onUpdateScore\",\n      onDataChange: \"onDataChange\"\n    },\n    decls: 10,\n    vars: 9,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [1, \"card\"], [1, \"card-header\", \"px-0\", \"pt-0\"], [\"ngbNav\", \"\", \"class\", \"nav-tabs\", 4, \"ngIf\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"fields_subject\", \"onSuccess\", \"onClose\"], [\"modalAssignReferee\", \"\"], [\"ngbNav\", \"\", 1, \"nav-tabs\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\", 3, \"click\"], [3, \"isMultipleAssign\", \"selectedIds\", \"listReferees\", \"assignRefereeForm\", \"assignRefereeFields\", \"assignRefereeModel\", \"onSubmit\"]],\n    template: function StageMatchesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, StageMatchesComponent_ul_4_Template, 10, 6, \"ul\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6)(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵlistener(\"onSuccess\", function StageMatchesComponent_Template_app_editor_sidebar_onSuccess_7_listener($event) {\n          return ctx.onSuccess($event);\n        })(\"onClose\", function StageMatchesComponent_Template_app_editor_sidebar_onClose_7_listener($event) {\n          return ctx.onCloseSidebar($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, StageMatchesComponent_ng_template_8_Template, 1, 6, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"paramsToPost\", ctx.paramsToPost)(\"fields_subject\", ctx.fields_subject);\n      }\n    },\n    dependencies: [i12.NgIf, i11.NgbNav, i11.NgbNavItem, i11.NgbNavLink, i13.CoreSidebarComponent, i14.DataTableDirective, i15.EditorSidebarComponent, i16.ModalAssignRefereesComponent, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAAA,SAASA,WAAW,QAAQ,yCAAyC;AAErE,SAEEC,YAAY,QAOP,eAAe;AAKtB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,IAAI,MAAM,aAAa;AAK9B,OAAOC,MAAM,MAAM,QAAQ;AAI3B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;ICzBlCC,iCAKC;IAEiBA;MAAAA;MAAA;MAAA,OAASA,4CAAsB,KAAK,CAAC;IAAA,EAAC;IAACA,YAEjD;;IAAAA,iBAAI;IAEVA,8BAAe;IACCA;MAAAA;MAAA;MAAA,OAASA,4CAAsB,IAAI,CAAC;IAAA,EAAC;IAACA,YAEhD;;IAAAA,iBAAI;;;IAP6CA,eAEjD;IAFiDA,yDAEjD;IAGgDA,eAEhD;IAFgDA,8DAEhD;;;;;;IAiCdA,qDAQE;IADAA;MAAAA;MAAA;MAAA,OAAYA,mDAA6B;IAAA,EAAC;IAP5CA,iBAQE;;;;IAPAA,0DAAqC;;;ADbzC,OAAM,MAAOC,qBAAqB;EAkBhC;EAEAC,YACUC,KAAiB,EAClBC,MAAsB,EACtBC,iBAAmC,EACnCC,eAA+B,EAC/BC,aAA2B,EAC3BC,YAAyB,EACzBC,eAA+B,EAC/BC,mBAAuC,EACvCC,OAAsB,EACrBC,cAA6B,EAC7BC,aAAuB;IAVvB,UAAK,GAALV,KAAK;IACN,WAAM,GAANC,MAAM;IACN,sBAAiB,GAAjBC,iBAAiB;IACjB,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,iBAAY,GAAZC,YAAY;IACZ,oBAAe,GAAfC,eAAe;IACf,wBAAmB,GAAnBC,mBAAmB;IACnB,YAAO,GAAPC,OAAO;IACN,mBAAc,GAAdC,cAAc;IACd,kBAAa,GAAbC,aAAa;IA7BvB,cAAS,GAAQjB,kBAAkB;IAGnC,cAAS,GAAQ,EAAE;IACnB,cAAS,GAAyB,IAAIF,OAAO,EAAe;IAC5D,cAAS,GAAGC,SAAS;IAErB,aAAQ,GAAY,KAAK;IACjB,eAAU,GAAY,KAAK;IAEzB,kBAAa,GAAG,IAAIF,YAAY,EAAO;IACvC,iBAAY,GAAG,IAAIA,YAAY,EAAO;IAGhD,iBAAY,GAAG,EAAE;IAmBjB,0BAAqB,GAAG,KAAK;IACtB,mBAAc,GAAG,IAAIC,OAAO,EAAO;IACnC,kBAAa,GAAG,EAAE;IAClB,iBAAY,GAAG,EAAE;IACjB,qBAAgB,GAAG,KAAK;IACxB,eAAU,GAAG,eAAe;IAC5B,WAAM,GAAwB;MACnCoB,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACZ,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QACnDC,IAAI,EAAE,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QAClDE,MAAM,EAAE,IAAI,CAACf,iBAAiB,CAACa,OAAO,CAAC,cAAc;OACtD;MACDG,GAAG,EAAE,GAAG7B,WAAW,CAAC8B,MAAM,uBAAuB;MACjDC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IACM,aAAQ,GAAG;MAChBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;KACX;IACM,UAAK,GAAG;MACbD,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;KACX;IAED,mBAAc,GAAG,EAAE;IAEZ,cAAS,GAAG,CACjB;MACEC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,MAAM,CAAC;QAC7Ca,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACzD;QACAc,GAAG,EAAE;OACN;MACDC,WAAW,EAAE;QACX,gBAAgB,EACd;;KAEL,EACD;MACEN,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CACzC,2BAA2B,CAC5B;QACD;QACAU,IAAI,EAAE;OACP;MACDK,WAAW,EAAE;QACX,gBAAgB,EACd;;KAEL,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACjDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,yBAAyB,CAAC;QACtE;QACAU,IAAI,EAAE;;KAET,EAED;MACED,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACjDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,iBAAiB,CAAC;QAC9D;QACAO,OAAO,EAAE,IAAI,CAACS,QAAQ,CAACT;;KAE1B,EAED;MACEE,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLM,cAAc,EAAE,IAAI;QACpBL,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QAClDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,kBAAkB,CAAC;QAC/DO,OAAO,EAAE;;KAEZ,EACD;MACEE,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLM,cAAc,EAAE,IAAI;QACpBL,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QAClDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,kBAAkB,CAAC;QAC/DO,OAAO,EAAE;;KAEZ,CACF;IAEM,gBAAW,GAAG,EAAE;IAEhB,gBAAW,GAAG,CACnB;MACEE,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,aAAa,CAAC;QACpDkB,QAAQ,EAAE,IAAI;QACdX,OAAO,EAAE,IAAI,CAACY;;KAEjB,EACD;MACEV,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,QAAQ,CAAC;QAC/Ca,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,cAAc;;KAE7D,CACF;IAEM,oBAAe,GAAG,CACvB;MACES,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLM,cAAc,EAAE,IAAI;QACpBL,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,mBAAmB,CAAC;QAChEO,OAAO,EAAE;;KAEZ,EACD;MACEE,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLM,cAAc,EAAE,IAAI;QACpBL,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,mBAAmB,CAAC;QAChEO,OAAO,EAAE;;KAEZ,CACF;IAEM,WAAM,GAAU,IAAI,CAACa,SAAS;IAE9B,gBAAW,GAAU,IAAI,CAACC,eAAe;IAgvCzC,qBAAgB,GAAG,IAAI;IACvB,gBAAW,GAAG,EAAE;IAChB,sBAAiB,GAAG,IAAIxC,SAAS,CAAC,EAAE,CAAC;IACrC,uBAAkB,GAAG,EAAE;IACvB,wBAAmB,GAAG,CAC3B;MACE4B,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLW,QAAQ,EAAE,IAAI;QACdL,cAAc,EAAE,IAAI;QACpBM,YAAY,EAAE,EAAE;QAChBX,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;QACtDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,uBAAuB,CAAC;QACpEO,OAAO,EAAE;OACV;MACDiB,KAAK,EAAE;KACR,CACF;EA15CD;EA0JAC,QAAQ;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,gBAAgB;IACd,MAAMI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACrD,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACxCM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,YAAY,CAAC;KAChD,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEAV,KAAK;IACHlD,SAAS,CAAC6D,kBAAkB,CAACC,OAAO,CAAE7B,IAAI,IAAI;MAC5C,IAAI,CAACS,aAAa,CAACqB,IAAI,CAAC;QACtBC,KAAK,EAAE/B,IAAI;QACXE,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAACU,IAAI;OAC3C,CAAC;IACJ,CAAC,CAAC;IACD,IAAI,CAACU,SAAiB,CAACoB,IAAI,CAAC;MAC3B/B,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;OACP;MACDa,YAAY,EAAE,IAAI,CAACmB,KAAK,CAACC;KAC1B,CAAC;IAEF,IAAI,CAACC,WAAW,GAAG,CACjB;MACEnC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;OACP;MACDa,YAAY,EAAE,IAAI,CAACmB,KAAK,CAAChC;KAC1B,EACD;MACED,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDkB,QAAQ,EAAE,IAAI;QACdR,IAAI,EAAE,QAAQ;QACdmC,GAAG,EAAE,CAAC;QACN/B,GAAG,EAAE,GAAG;QACRgC,IAAI,EAAE;OACP;MACDvB,YAAY,EAAE;KACf,EACD;MACEd,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDkB,QAAQ,EAAE,IAAI;QACdR,IAAI,EAAE,QAAQ;QACdmC,GAAG,EAAE,CAAC;QACN/B,GAAG,EAAE,GAAG;QACRgC,IAAI,EAAE;OACP;MACDvB,YAAY,EAAE;KACf,EACD;MACEd,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,oBAAoB,CAAC;QAC3D;QACAU,IAAI,EAAE,QAAQ;QACdmC,GAAG,EAAE,CAAC;QACN/B,GAAG,EAAE,GAAG;QACRgC,IAAI,EAAE;OACP;MACD/B,WAAW,EAAE;QACXgC,IAAI,EAAE,kBAAkBtE,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAAChB,YAAY,CAACiB,UAAU;OACzI;MACD3B,YAAY,EAAE;KACf,EACD;MACEd,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,oBAAoB,CAAC;QAC3D;QACAU,IAAI,EAAE,QAAQ;QACdmC,GAAG,EAAE,CAAC;QACN/B,GAAG,EAAE,GAAG;QACRgC,IAAI,EAAE;OACP;MACD/B,WAAW,EAAE;QACXgC,IAAI,EAAE,kBAAkBtE,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,iDAAiD,IAAI,CAAChB,YAAY,CAACiB,UAAU;OACzI;MACD3B,YAAY,EAAE;KACf,CACF;IAED,IAAI,CAAC4B,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACjE,iBAAiB,CAACa,OAAO,CAAC,gBAAgB,CAAC;MAC7DqD,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV5C,IAAI,EAAE,EAAE;QACR6C,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACrE,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;UAC/CyD,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACrE,iBAAiB,CAACa,OAAO,CAAC,gBAAgB,CAAC;UACtDyD,MAAM,EAAE;SACT;;KAGN;IAED,IACE,IAAI,CAACf,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,IACrD,IAAI,CAAChB,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACW,MAAM,EACrD;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAAClB,KAAK,CAACmB,aAAa,EAAED,CAAC,EAAE,EAAE;QAClD,IAAI,CAACE,cAAc,CAACtB,IAAI,CAAC;UACvBC,KAAK,EAAE,GAAGmB,CAAC,EAAE;UACbhD,KAAK,EAAE,GAAG,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC,IAAI4D,CAAC;SACvD,CAAC;;;IAIN,IAAI,IAAI,CAAClB,KAAK,CAAChC,IAAI,IAAIjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EAAE;MACxD;MACC,IAAI,CAACtC,SAAiB,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACnCtD,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE;UACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;UACnDkB,QAAQ,EAAE,IAAI;UACdX,OAAO,EAAE,CACP;YACEkC,KAAK,EAAE,CAAC;YACR7B,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,cAAc;WACrD,EACD;YACEyC,KAAK,EAAE,CAAC;YACR7B,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,gBAAgB;WACvD;;OAGN,CAAC;;IAGJ,IACE,IAAI,CAAC0C,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,EACvD;MACA;MACA,IAAIe,eAAe,GAAI,IAAI,CAAC5C,SAAiB,CAAC6C,SAAS,CACpDC,KAAU,IAAKA,KAAK,CAACzD,GAAG,KAAK,aAAa,CAC5C;MACD;MACA,IAAIuD,eAAe,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC5C,SAAiB,CAAC2C,MAAM,CAACC,eAAe,EAAE,CAAC,CAAC;;MAGpD;MACC,IAAI,CAAC5C,SAAiB,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACnCtD,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACLM,cAAc,EAAE,IAAI;UACpBL,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;UACnDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,kBAAkB;;OAEjE,CAAC;;EAEN;EAEA4B,UAAU;IAAA;IACR,IAAIuC,IAAI,GAAG,CACT;MACEC,IAAI,EAAE,oCAAoC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACtE,KAAK,CACN,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAI,IAAI,CAAC9B,KAAK,CAAChC,IAAI,IAAIjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EAAE;UACxD/E,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,qCAAqC,CACtC;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAGF,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;MACpC;KACD,EACD;MACEC,IAAI,EAAE;QACJpC,EAAE,EAAE;OACL;MACDyB,IAAI,EAAE,mDAAmD,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACrF,eAAe,CAChB,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B,IAAI,CAACQ,mBAAmB,EAAE;MAC5B;KACD,EACD;MACEZ,IAAI,EAAE,qCAAqC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACvE,cAAc,CACf,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD,IAAIC,GAAG,GAAGH,YAAY,CAAC,CAAC,CAAC;QACzB,IAAII,WAAW,GAAG,KAAK;QACvB,IAAI,IAAI,CAACC,wBAAwB,CAACF,GAAG,CAAC,EAAE;UACtCC,WAAW,GAAG,IAAI;;QAEpB,IAAI,CAACA,WAAW,EAAE;UAChB1G,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,qBAAqB,CAAC;YAC5DoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,wDAAwD,CACzD;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;SACH,MAAM;UACL,IAAI,CAACC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;;MAEtC,CAAC;MACDS,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,2CAA2C,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAC7E,aAAa,CACd,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QAErD;QACA,IAAIK,GAAG,GAAG,EAAE;QAEZP,YAAY,CAACQ,GAAG,CAAEL,GAAG,IAAKI,GAAG,CAAChD,IAAI,CAAC4C,GAAG,CAACzC,EAAE,CAAC,CAAC;QAE3C;QACAhE,IAAI,CAAC8F,IAAI,CAAC;UACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;UACtDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,qDAAqD,CACtD;UACD0F,cAAc,EAAE,IAAI;UACpBhB,IAAI,EAAE,SAAS;UACfiB,gBAAgB,EAAE,IAAI;UACtBhB,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;UACxD4F,gBAAgB,EAAE,IAAI,CAACzG,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;UACtD6F,cAAc,EAAE,KAAK;UACrBjB,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCiB,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACvD,KAAK,EAAE;YAChB,IAAI,CAACwD,UAAU,CAACT,GAAG,CAAC;;QAExB,CAAC,CAAC;MACJ,CAAC;MACDD,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,oCAAoC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACtE,gBAAgB,CACjB,EAAE;MACHM,MAAM,EAAE,MAAM,IAAI,CAAC4F,SAAS,EAAE;MAC9BX,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,oCAAoC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACtE,MAAM,CACP,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QAErD,IAAIF,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,IAAInC,eAAe,GAAI,IAAI,CAAC5C,SAAiB,CAAC6C,SAAS,CACpDmC,CAAC,IAAKA,CAAC,CAAC3F,GAAG,IAAI,aAAa,CAC9B;UACD,IAAIuD,eAAe,GAAG,CAAC,CAAC,EAAE;YACvB,IAAI,CAAC5C,SAAiB,CAAC2C,MAAM,CAACC,eAAe,EAAE,CAAC,CAAC;;UAGpD,IAAIqC,cAAc,GAAI,IAAI,CAACjF,SAAiB,CAAC6C,SAAS,CACnDmC,CAAC,IAAKA,CAAC,CAAC3F,GAAG,IAAI,YAAY,CAC7B;UAED,IAAI4F,cAAc,GAAG,CAAC,CAAC,EAAE;YACtB,IAAI,CAACjF,SAAiB,CAAC2C,MAAM,CAACsC,cAAc,EAAE,CAAC,CAAC;;SAEpD,MAAM;UACL;UAEA,IAAIrC,eAAe,GAAI,IAAI,CAAC5C,SAAiB,CAAC6C,SAAS,CACpDmC,CAAC,IAAKA,CAAC,CAAC3F,GAAG,IAAI,aAAa,CAC9B;UACD;UACA,IACEuD,eAAe,IAAI,CAAC,CAAC,IACrB,IAAI,CAACtB,KAAK,CAAChC,IAAI,IAAIjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EACpD;YACC,IAAI,CAACtC,SAAiB,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;cACnCtD,GAAG,EAAE,aAAa;cAClBC,IAAI,EAAE,QAAQ;cACdC,KAAK,EAAE;gBACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,aAAa,CAAC;gBACpDa,WAAW,EACT,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,oBAAoB,CAAC;gBACtD;gBACAO,OAAO,EAAE,IAAI,CAACuD;;aAEjB,CAAC;;UAEJ,IACE,IAAI,CAACpB,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,EACvD;YACA,IAAIoD,cAAc,GAAI,IAAI,CAACjF,SAAiB,CAAC6C,SAAS,CACnDmC,CAAC,IAAKA,CAAC,CAAC3F,GAAG,IAAI,YAAY,CAC7B;YAED,IAAI4F,cAAc,IAAI,CAAC,CAAC,EAAE;cACvB,IAAI,CAACjF,SAAiB,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;gBACnCtD,GAAG,EAAE,YAAY;gBACjBC,IAAI,EAAE,OAAO;gBACbC,KAAK,EAAE;kBACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;kBACnDa,WAAW,EACT,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,kBAAkB;;eAEtD,CAAC;;;;QAKR,IAAI,CAAC8E,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC;MAClC,CAAC;MACDS,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,wCAAwC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAC1E,mBAAmB,CACpB,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;UAC3BxH,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,8BAA8B,CAC/B;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAIO,GAAG,GAAGH,YAAY,CAAC,CAAC,CAAC;QACzB,IAAIqB,SAAS,GAAG,KAAK;QACrB,IAAI,IAAI,CAACC,mBAAmB,CAACnB,GAAG,CAAC,EAAE;UACjCkB,SAAS,GAAG,IAAI;;QAElB,IAAI,CAACA,SAAS,EAAE;UACd3H,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,8CAA8C,CAC/C;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,EAAEM,GAAG,CAAC;MAC/C,CAAC;MACDG,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,uCAAuC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACzE,YAAY,CACb,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;UAC3BxH,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,8BAA8B,CAC/B;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAGF;QAEA,IAAII,YAAY,CAAC,CAAC,CAAC,CAACuB,MAAM,KAAK,MAAM,KAAK,CAACvB,YAAY,CAAC,CAAC,CAAC,CAACwB,YAAY,IAAI,CAACxB,YAAY,CAAC,CAAC,CAAC,CAACyB,YAAY,CAAC,EAAE;UACzG/H,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,uCAAuC,CACxC;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;SACD,MAAM;UACL;UACAlG,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;YACtDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,oDAAoD,CACrD;YACD0E,IAAI,EAAE,SAAS;YACfgB,cAAc,EAAE,IAAI;YACpBC,gBAAgB,EAAE,IAAI;YACtBhB,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;YACxD4F,gBAAgB,EAAE,IAAI,CAACzG,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACtD6F,cAAc,EAAE,KAAK;YACrBjB,WAAW,EAAE;cACXC,aAAa,EAAE,sBAAsB;cACrCiB,YAAY,EAAE;;WAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACjB,IAAIA,MAAM,CAACvD,KAAK,EAAE;cAChB,IAAI,CAACkE,SAAS,CAAC1B,YAAY,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,CAAC;;MAEN,CAAC;MACDM,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,mCAAmC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACrE,QAAQ,CACT,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD;QACA,IAAImB,SAAS,GAAG,KAAK;QACrBrB,YAAY,CAACQ,GAAG,CAAEL,GAAG,IAAI;UACvB,IAAI,IAAI,CAACmB,mBAAmB,CAACnB,GAAG,CAAC,EAAE;YACjCkB,SAAS,GAAG,IAAI;;QAEpB,CAAC,CAAC;QAEF,IAAIA,SAAS,EAAE;UACb3H,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;YAChDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,kDAAkD,CACnD;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UAEF;;QAEF;QACAlG,IAAI,CAAC8F,IAAI,CAAC;UACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;UACtDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,gDAAgD,CACjD;UACD0F,cAAc,EAAE,IAAI;UACpBhB,IAAI,EAAE,SAAS;UACfiB,gBAAgB,EAAE,IAAI;UACtBhB,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;UACxD4F,gBAAgB,EAAE,IAAI,CAACzG,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;UACtD6F,cAAc,EAAE,KAAK;UACrBjB,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCiB,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACvD,KAAK,EAAE;YAChB,IAAI,CAACqC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;;QAEtC,CAAC,CAAC;MACJ,CAAC;MAEDS,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,sCAAsC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACxE,QAAQ,CACT,EAAE;MACHuF,MAAM,EAAE,UAAU;MAClBjF,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B;QACA,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD;QACA,IAAIK,GAAG,GAAG,EAAE;QACZP,YAAY,CAACQ,GAAG,CAAEL,GAAG,IAAI;UACvBI,GAAG,CAAChD,IAAI,CAAC4C,GAAG,CAACzC,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF;QACAhE,IAAI,CAAC8F,IAAI,CAAC;UACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;UACtDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,uCAAuC,CACxC;UACD0F,cAAc,EAAE,IAAI;UACpBhB,IAAI,EAAE,SAAS;UACfiB,gBAAgB,EAAE,IAAI;UACtBhB,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;UACxD4F,gBAAgB,EAAE,IAAI,CAACzG,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;UACtD6F,cAAc,EAAE,KAAK;UACrBjB,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCiB,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACvD,KAAK,EAAE;YAChB;YACA,IAAI,CAAClD,eAAe,CAACqH,IAAI,EAAE;YAC3B,IAAI,CAACvH,aAAa,CACfwH,oBAAoB,CAACrB,GAAG,EAAE,IAAI,CAAC9C,KAAK,CAACC,EAAE,CAAC,CACxCmE,SAAS,EAAE,CACXf,IAAI,CAAEgB,IAAI,IAAI;cACb,IAAI,CAACtH,OAAO,CAACuH,OAAO,CAClB,IAAI,CAAC7H,iBAAiB,CAACa,OAAO,CAAC,sBAAsB,CAAC,CACvD;cACDsE,EAAE,CAAC2C,IAAI,CAACC,MAAM,EAAE;cAChB,IAAI,CAACC,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;YAC9B,CAAC,CAAC;;QAER,CAAC,CAAC;MACJ;KACD,EACD;MACE3C,IAAI,EAAE,2CAA2C,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAC7E,QAAQ,CACT,EAAE;MACHuF,MAAM,EAAE,KAAK;MACbjF,MAAM;QAAA,6BAAE,WAAO+D,CAAM,EAAEC,EAAO,EAAE+C,MAAW,EAAE7C,MAAW,EAAI;UAC1D,MAAMW,IAAI,GAAGb,EAAE,CAACgD,OAAO,CAACC,UAAU,EAAE;UACpC,MAAM,KAAI,CAAC7H,cAAc,CAAC8H,SAAS,CAACrC,IAAI,EAAE,aAAa,CAAC;QAC1D,CAAC;QAAA,gBAHD7E,MAAM;UAAA;QAAA;MAAA;KAIP,EACD;MACE8D,IAAI,EAAE,oCAAoC,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CACtE,aAAa,CACd,EAAE;MACHM,MAAM,EAAE,CAAC+D,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC9B,IAAIS,YAAY,GAAGX,EAAE,CAACY,IAAI,CAAC;UAAE1E,QAAQ,EAAE;QAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;QACrD;QACA,IAAIF,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;UAC3BxH,IAAI,CAAC8F,IAAI,CAAC;YACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;YACrDoE,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAClC,8BAA8B,CAC/B;YACD0E,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;YACvD4E,WAAW,EAAE;cACXC,aAAa,EAAE;;WAElB,CAAC;UACF;;QAEF,IAAI,CAACC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC;MACxC,CAAC;MACDS,MAAM,EAAE;KACT,EACD;MACEnB,IAAI,EAAE,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;MAC/CuF,MAAM,EAAE;KACT,CACF;IAED;IACA,IAAI,IAAI,CAAC7C,KAAK,CAAChC,IAAI,IAAIjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EAAE;MACxD;MACAS,IAAI,CAACJ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGnB,IACE,IAAI,CAACrB,KAAK,CAAChC,IAAI,IAAIjC,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,IACtD,IAAI,CAACwE,UAAU,CAACC,aAAa,KAAKjJ,SAAS,CAACkJ,cAAc,CAACC,KAAK,EAChE;MACAzD,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGpB,IACE,IAAI,CAAC0D,UAAU,CAACC,aAAa,IAAIjJ,SAAS,CAACkJ,cAAc,CAACC,KAAK,IAC/D,IAAI,CAAClF,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,EACvD;MACAkB,IAAI,CAACJ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACjBI,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGpB,IAAI,CAAC8D,SAAS,GAAG;MACfC,GAAG,EAAE,IAAI,CAAC1I,eAAe,CAAC2I,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAEC,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,IAAI,GAAG,OAAO;MAChD;MACAC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAa,CAAE;MACpCC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACnBrB,IAAI,EAAE,CAACsB,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAI,CAACvJ,KAAK,CACPwJ,IAAI,CACH,GAAGnK,WAAW,CAAC8B,MAAM,+BAA+B,IAAI,CAACsC,KAAK,CAACC,EAAE,EAAE,EACnE4F,oBAAoB,CACrB,CACAG,SAAS,CAAE3B,IAAS,IAAI;UACvB;UACA,IAAI,CAAC4B,MAAM,CAACpG,OAAO,CAAE2B,KAAK,IAAI;YAC5B,IAAIA,KAAK,CAACzD,GAAG,KAAK,aAAa,EAAE;cAC/ByD,KAAK,CAACvD,KAAK,CAACJ,OAAO,GAAGwG,IAAI,CAACxG,OAAO,CAACS,QAAQ;;YAE7C,IAAIkD,KAAK,CAACzD,GAAG,KAAK,cAAc,EAAE;cAChCyD,KAAK,CAACvD,KAAK,CAACJ,OAAO,GAAGwG,IAAI,CAACxG,OAAO,CAACqI,KAAK;;YAE1C,IAAI1E,KAAK,CAACzD,GAAG,KAAK,cAAc,EAAE;cAChCyD,KAAK,CAACvD,KAAK,CAACJ,OAAO,GAAGwG,IAAI,CAACxG,OAAO,CAACqI,KAAK;;UAE5C,CAAC,CAAC;UAEF,IAAI,CAACC,WAAW,CAACtG,OAAO,CAAE2B,KAAK,IAAI;YACjC,IAAIA,KAAK,CAACzD,GAAG,KAAK,eAAe,EAAE;cACjCyD,KAAK,CAACvD,KAAK,CAACJ,OAAO,GAAGwG,IAAI,CAACxG,OAAO,CAACuI,UAAU;;YAE/C,IAAI5E,KAAK,CAACzD,GAAG,KAAK,eAAe,EAAE;cACjCyD,KAAK,CAACvD,KAAK,CAACJ,OAAO,GAAGwG,IAAI,CAACxG,OAAO,CAACuI,UAAU;;UAEjD,CAAC,CAAC;UAEF,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACC,qBAAqB,CAAC;UAEtD,IAAI,CAACC,QAAQ,GAAGlC,IAAI,CAAC5B,IAAI,CAACgB,MAAM,GAAG,CAAC;UAEpCqC,QAAQ,CAAC;YACPU,YAAY,EAAEnC,IAAI,CAACmC,YAAY;YAC/BC,eAAe,EAAEpC,IAAI,CAACoC,eAAe;YACrChE,IAAI,EAAE4B,IAAI,CAAC5B;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDiE,YAAY,EAAE,MAAK;QACjB,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC;MACDC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAACpK,eAAe,CAAC2I,iBAAiB,CAAC0B,IAAI;MACrDC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CACvC;MACDC,OAAO,EAAE,CACP;QAAE1E,IAAI,EAAE,OAAO;QAAE2E,OAAO,EAAE;MAAK,CAAE,EACjC;QAAE3E,IAAI,EAAE,YAAY;QAAE2E,OAAO,EAAE;MAAK,CAAE,EACtC;QAAE3E,IAAI,EAAE,aAAa;QAAE2E,OAAO,EAAE;MAAK,CAAE,EACvC;QACEhK,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;QAC3CmF,IAAI,EAAE,cAAc;QACpB2E,OAAO,EAAE,IAAI,CAACpH,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM;QAC9DqG,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG,EAAE4E,IAAI;UACrC,OAAO7E,IAAI,GAAG,GAAGA,IAAI,EAAE,GAAG,GAAG6E,IAAI,CAAC5E,GAAG,GAAG,CAAC,EAAE;QAC7C;OACD,EACD;QACEtF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,MAAM,CAAC;QAC7CmF,IAAI,EAAE,MAAM;QACZ8E,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,iBAAiB;QAC5BH,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAI+E,OAAO,GAAG,EAAE;UAChB,IAAI,CAAChF,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;YAC3BgF,OAAO,GAAG,KAAK;WAChB,MAAM;YACL;YACAA,OAAO,GAAGvL,MAAM,CAACuG,IAAI,CAAC,CAACiF,MAAM,CAAC,YAAY,CAAC;;UAE7C,OAAO,+CAA+CD,OAAO,MAAM;QACrE;OACD,EACD;QACErK,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDmF,IAAI,EAAE,kBAAkB;QACxB8E,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,iBAAiB;QAC5BH,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IACE,CAACD,IAAI,IACLC,GAAG,CAACiF,gBAAgB,IAAI,KAAK,IAC7B,CAACjF,GAAG,CAACiF,gBAAgB,EACrB;YACA,OAAO,KAAK;;UAEd;UACA,OAAOlF,IAAI;QACb;OACD,EACD;QACErF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACjDmF,IAAI,EAAE,gBAAgB;QACtB8E,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,iBAAiB;QAC5BH,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAI,CAACD,IAAI,IAAIC,GAAG,CAACkF,cAAc,IAAI,KAAK,IAAI,CAAClF,GAAG,CAACkF,cAAc,EAAE;YAC/D,OAAO,KAAK;;UAEd;UACA,OAAOnF,IAAI;QACb;OACD,EACD;QACErF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACjDmF,IAAI,EAAE;OACP,EACD;QACErF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QAClDmF,IAAI,EAAE,gBAAgB;QACtB+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAID,IAAI,KAAK,KAAK,EAAE;YAClB,IAAIC,GAAG,CAACmF,SAAS,EAAE;cACjB,OAAOnF,GAAG,CAACmF,SAAS;aACrB,MAAM,IAAInF,GAAG,CAACoF,UAAU,IAAIpF,GAAG,CAACoF,UAAU,CAAC5J,KAAK,EAAE;cACjD,OAAOwE,GAAG,CAACoF,UAAU,CAAC5J,KAAK;;;UAG/B,OAAOuE,IAAI;QACb;OACD,EACD;QACEA,IAAI,EAAE,IAAI;QACV+E,SAAS,EAAE,oCAAoC;QAC/CH,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,OAAO,IAAI;QACb,CAAC;QACD6E,SAAS,EAAE;OACZ,EACD;QACEnK,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QAClDmF,IAAI,EAAE,gBAAgB;QACtB+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAID,IAAI,KAAK,KAAK,EAAE;YAClB,IAAIC,GAAG,CAACqF,SAAS,EAAE;cACjB,OAAOrF,GAAG,CAACqF,SAAS;aACrB,MAAM,IAAIrF,GAAG,CAACsF,UAAU,IAAItF,GAAG,CAACsF,UAAU,CAAC9J,KAAK,EAAE;cACjD,OAAOwE,GAAG,CAACsF,UAAU,CAAC9J,KAAK;;;UAG/B,OAAOuE,IAAI;QACb;OACD,EACD;QACErF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDmF,IAAI,EAAE,YAAY;QAClB+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAIA,GAAG,CAACuF,YAAY,IAAI,IAAI,EAAE;YAC5B,OAAO,GAAGxF,IAAI,UAAUC,GAAG,CAACuF,YAAY,IAAI;;UAE9C,OAAOxF,IAAI;QACb;OACD,EACD;QACEA,IAAI,EAAE,IAAI;QACV+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,OAAO,KAAK;QACd;OACD,EACD;QACEtF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;QACnDmF,IAAI,EAAE,YAAY;QAClB+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE,KAAK;QAChBF,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;UAC/B,IAAIA,GAAG,CAACwF,YAAY,IAAI,IAAI,EAAE;YAC5B,OAAO,GAAGzF,IAAI,UAAUC,GAAG,CAACwF,YAAY,IAAI;;UAE9C,OAAOzF,IAAI;QACb;OACD,EACD;QACErF,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACjD8J,OAAO,EAAE,KAAK;QACd3E,IAAI,EAAE,UAAU;QAChB4E,MAAM,EAAG5E,IAAI,IAAI;UACf,OAAO;gBACHA,IAAI,CAACM,GAAG,CAAEoF,IAAI,IAAI;YACpB,MAAMC,WAAW,GAAGD,IAAI,CAACE,IAAI,GAAG,GAAGF,IAAI,CAACE,IAAI,CAACC,UAAU,IAAIH,IAAI,CAACE,IAAI,CAACE,SAAS,EAAE,GAAGJ,IAAI,CAACK,YAAY;YACpG,OAAO,gDAAgDJ,WAAW,MAAM;UAC1E,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC;mBACJ;QACT;OACD,EACD;QACErL,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,QAAQ,CAAC;QAC/CmF,IAAI,EAAE,QAAQ;QACd4E,MAAM,EAAE,CAAC5E,IAAY,EAAEzE,IAAI,EAAE0E,GAAG,KAAI;UAClC,MAAMgG,GAAG,GAAGxM,MAAM,EAAE;UACpB,MAAMyM,WAAW,GAAGD,GAAG,CAAChB,MAAM,CAAC,OAAO,CAAC;UACvC,MAAMkB,KAAK,GAAGF,GAAG,CAAChB,MAAM,CAAC,YAAY,CAAC;UACtC,IAAI,CAACjF,IAAI,EAAE;YACT;YACA,IAAIC,GAAG,CAACkF,cAAc,IAAI,KAAK,EAAE;cAC/B,OAAO,gEAAgE;aACxE,MAAM;cACL,IACGlF,GAAG,CAACmG,IAAI,IAAID,KAAK,IAAIlG,GAAG,CAACkF,cAAc,GAAGe,WAAW,IACtDjG,GAAG,CAACmG,IAAI,GAAGD,KAAK,EAChB;gBACA,OAAO,qDAAqD,IAAI,CAACnM,iBAAiB,CAACa,OAAO,CACxF,UAAU,CACX,SAAS;eACX,MAAM,IACLoF,GAAG,CAACmG,IAAI,IAAID,KAAK,IACjBlG,GAAG,CAACiF,gBAAgB,IAAIgB,WAAW,IACnCjG,GAAG,CAACkF,cAAc,IAAIe,WAAW,EACjC;gBACA,OAAO,qDAAqD,IAAI,CAAClM,iBAAiB,CAACa,OAAO,CACxF,aAAa,CACd,SAAS;eACX,MAAM,IACLoF,GAAG,CAACmG,IAAI,GAAGD,KAAK,IACflG,GAAG,CAACmG,IAAI,IAAID,KAAK,IAAIlG,GAAG,CAACiF,gBAAgB,GAAGgB,WAAY,EACzD;gBACA,OAAO,kDAAkD,IAAI,CAAClM,iBAAiB,CAACa,OAAO,CACrF,UAAU,CACX,SAAS;;;WAGf,MAAM;YACL,IAAIvB,SAAS,CAAC6D,kBAAkB,CAACkJ,QAAQ,CAACrG,IAAI,CAAC,EAAE;cAC/C,OAAO,oDAAoDA,IAAI,SAAS;aACzE,MAAM;cACL,OAAO,uDAAuDA,IAAI,SAAS;;;QAGjF,CAAC;QACD+E,SAAS,EAAE,iBAAiB;QAC5BD,SAAS,EAAE;OACZ,CACF;MACDwB,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EACjB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CACrB;MACDnE,OAAO,EAAE;QACPQ,GAAG,EAAE,IAAI,CAAC1I,eAAe,CAAC2I,iBAAiB,CAACT,OAAO,CAACQ,GAAG;QACvDR,OAAO,EAAEnD;;KAEZ;IAED,QAAQ,IAAI,CAACzB,KAAK,CAAChC,IAAI;MACrB,KAAKjC,SAAS,CAACuE,gBAAgB,CAACC,QAAQ;QACtC,IAAI,CAAC4E,SAAS,CAACS,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC;QACA,IAAI,CAACT,SAAS,CAACO,QAAQ,GAAG;UAAEC,OAAO,EAAE;QAAY,CAAE;QACnD;QACA,IAAI,CAACR,SAAS,CAACgC,OAAO,CAAC9F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCjE,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,MAAM,CAAC;UAC7CmF,IAAI,EAAE,YAAY;UAClB+E,SAAS,EAAE,aAAa;UACxBH,MAAM,EAAE,UAAU5E,IAAI;YACpB,OAAO,+CAA+CA,IAAI,MAAM;UAClE;SACD,CAAC;QAEF;MACF,KAAK1G,SAAS,CAACuE,gBAAgB,CAACW,MAAM;QACpC,IAAI,CAACkE,SAAS,CAACS,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnC;QACA,IAAI,CAACT,SAAS,CAACgC,OAAO,CAAC9F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCjE,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC;UAC9CmF,IAAI,EAAE,YAAY;UAClB+E,SAAS,EAAE,aAAa;UACxBH,MAAM,EAAE,UAAU5E,IAAI,EAAEzE,IAAI,EAAE0E,GAAG;YAC/B;YACA,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;YACpB,IAAIuG,UAAU,GAAGvG,IAAI,CAACwG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;YACtC,OAAO,+CAA+CF,UAAU,MAAM;UACxE;SACD,CAAC;QACF;MACF,KAAKjN,SAAS,CAACuE,gBAAgB,CAACU,MAAM;QACpC;QACA,IAAI,CAACmE,SAAS,CAACO,QAAQ,GAAG,IAAI;QAC9B,IAAI,CAACP,SAAS,CAACgC,OAAO,CAAC9F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;UAClCjE,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC;UAC9CmF,IAAI,EAAE,YAAY;UAClB+E,SAAS,EAAE,aAAa;UACxBH,MAAM,EAAE,UAAU5E,IAAI;YACpB/C,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE8C,IAAI,CAAC;YACzB,OAAO,+CAA+CA,IAAI,IAAI,EAAE,MAAM;UACxE;SACD,CAAC;QACF;QACA,IAAI,CAAC0C,SAAS,CAACS,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC;MAEF;QACE;IAAM;EAEZ;EAEAuD,qBAAqB,CAACC,WAAoB;IACxC,MAAMC,WAAW,GAAG;MAClBtL,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,aAAa,CAAC;QACpDa,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAACa,OAAO,CAAC,oBAAoB,CAAC;QACjE;QACAO,OAAO,EAAE,IAAI,CAACuD;;KAEjB;IAED,IAAIgI,WAAW,EAAE;MACf,IAAI,IAAI,CAACpJ,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EAAE;QACzD;QACA,MAAMsI,iBAAiB,GAAI,IAAI,CAAC5K,SAAiB,CAAC6C,SAAS,CACxD4G,IAAI,IAAKA,IAAI,CAACpK,GAAG,KAAK,aAAa,CACrC;QACD;QACA,IAAIuL,iBAAiB,GAAG,CAAC,CAAC,EAAE;UACzB,IAAI,CAAC5K,SAAiB,CAAC2C,MAAM,CAACiI,iBAAiB,EAAE,CAAC,CAAC;;;KAGzD,MAAM;MACL,IAAI,IAAI,CAACtJ,KAAK,CAAChC,IAAI,KAAKjC,SAAS,CAACuE,gBAAgB,CAACU,MAAM,EAAE;QACzD,MAAMsI,iBAAiB,GAAI,IAAI,CAAC5K,SAAiB,CAAC6C,SAAS,CACxD4G,IAAI,IAAKA,IAAI,CAACpK,GAAG,KAAK,aAAa,CACrC;QACD;QACA,IAAIuL,iBAAiB,GAAG,CAAC,EAAE;UACxB,IAAI,CAAC5K,SAAiB,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;YACnC,GAAGgI;WACJ,CAAC;;;;EAIV;EAEAhD,qBAAqB,CAACkD,MAAM;IAC1B,IAAI,CAACjD,qBAAqB,GAAGiD,MAAM;IACnC,IAAI,CAACJ,qBAAqB,CAACI,MAAM,CAAC;IAClC;IACA,IAAI,CAACC,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAAgC,IAAI;MAClE;MACAA,UAAU,CAACjH,IAAI,EAAE,CAACkH,QAAQ,EAAE;MAC5B,IAAI,CAACH,MAAM,EAAE;QACX;QACAE,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACvC,OAAO,CAAC,IAAI,CAAC;QAClCqC,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;QACrC;QACA,IAAIpI,IAAI,GAAGgI,UAAU,CAAC9E,MAAM,CAAC,oBAAoB,CAAC;QAClDjF,OAAO,CAACC,GAAG,CAAC8B,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAACqI,MAAM,EAAE;;OAEhB,MAAM;QACL;QACAL,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACvC,OAAO,CAAC,IAAI,CAAC;QAClCqC,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;QACrC;QACA,IAAIpI,IAAI,GAAGgI,UAAU,CAAC9E,MAAM,CAAC,oBAAoB,CAAC;QAClDjF,OAAO,CAACC,GAAG,CAAC8B,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAACsI,OAAO,EAAE;;;IAGpB,CAAC,CAAC;EACJ;EAEAlG,mBAAmB,CAACnB,GAAG;IACrB,IAAIkB,SAAS,GAAG,KAAK;IACrB;IACA,IAAI7H,SAAS,CAAC6D,kBAAkB,CAACkJ,QAAQ,CAACpG,GAAG,CAACoB,MAAM,CAAC,EAAE;MACrDF,SAAS,GAAG,IAAI;;IAElB,OAAOA,SAAS;EAClB;EAEQoG,gBAAgB;IACtB,IAAI,CAACrN,aAAa,CAAC2F,mBAAmB,CAAC,IAAI,CAACtC,KAAK,CAACC,EAAE,CAAC,CAAC+F,SAAS,CAC5D3B,IAAS,IAAI;MACZ,IAAI,CAACmF,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAA0B,IAAI;QAC5DA,UAAU,CAAClF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACC,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EACA4F,KAAK,IAAI;MACR,IAAIA,KAAK,CAACC,OAAO,EAAE;QACjBjO,IAAI,CAAC8F,IAAI,CAAC;UACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,sBAAsB,CAAC;UAC7D6M,IAAI,EAAEF,KAAK,CAACC,OAAO,CAACE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;UAC1CpI,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;UACvD4E,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;QACF;OACD,MAAM;QACLlG,IAAI,CAAC8F,IAAI,CAAC;UACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC;UAC9CoE,IAAI,EAAEuI,KAAK,CAACI,OAAO;UACnBrI,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;UACvD4E,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;;IAEN,CAAC,CACF;EACH;EAEAG,mBAAmB;IACjB5C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC4G,QAAQ,CAAC;IACnD;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjB,IAAI,CAAC1J,eAAe,CAACqH,IAAI,EAAE;MAE3BjI,IAAI,CAAC8F,IAAI,CAAC;QACR3E,KAAK,EAAE,GAAG,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC,EAAE;QAC3D6M,IAAI,EAAE;qBACO,IAAI,CAAC1N,iBAAiB,CAACa,OAAO,CACzC,qEAAqE,CACtE;;;uEAG8D,IAAI,CAACb,iBAAiB,CAACa,OAAO,CAC3F,uDAAuD,CACxD;;;aAGI;QACL0E,IAAI,EAAE,SAAS;QACfiB,gBAAgB,EAAE,IAAI;QACtBhB,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;QACxD4F,gBAAgB,EAAE,IAAI,CAACzG,iBAAiB,CAACa,OAAO,CAAC,IAAI;OACtD,CAAC,CAAC+F,IAAI,CAAEC,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACgH,WAAW,EAAE;UACtB,IAAI,CAACN,gBAAgB,EAAE;SACxB,MAAM;UACL,IAAI,CAACnN,eAAe,CAAC0N,OAAO,EAAE;;MAElC,CAAC,CAAC;KACH,MAAM;MACL,IAAI,IAAI,CAAC5D,UAAU,EAAE;QACnB,IAAI,CAACqD,gBAAgB,EAAE;;;EAG7B;EAEAzG,UAAU,CAACiH,cAAc;IACvB9K,OAAO,CAACC,GAAG,CAAC,mBAAmB6K,cAAc,EAAE,CAAC;IAChD,IAAI,CAAC3N,eAAe,CAACqH,IAAI,EAAE;IAC3B,IAAI,CAACvH,aAAa,CAAC4G,UAAU,CAACiH,cAAc,CAAC,CAACxE,SAAS,CACpD3B,IAAS,IAAI;MACZ3E,OAAO,CAACC,GAAG,CAAC0E,IAAI,CAAC;MAEjB,IAAI,CAACmF,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAA0B,IAAI;QAC5DA,UAAU,CAAClF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACC,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EACA4F,KAAK,IAAI;MACRhO,IAAI,CAAC8F,IAAI,CAAC;QACR3E,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC;QAC9CoE,IAAI,EAAEuI,KAAK,CAACI,OAAO;QACnBrI,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACxF,iBAAiB,CAACa,OAAO,CAAC,IAAI,CAAC;QACvD4E,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC;IACJ,CAAC,CACF;EACH;EAEA8B,SAAS,CAACwG,WAAuB;IAC/B,IAAI,CAAC5N,eAAe,CAACqH,IAAI,EAAE;IAC3B,IAAI,CAACvH,aAAa,CAACsH,SAAS,CAACwG,WAAW,CAAC,CAACzE,SAAS,CAAE3B,IAAS,IAAI;MAChE,IAAI,CAACmF,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAA0B,IAAI;QAC5DA,UAAU,CAAClF,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACC,YAAY,CAACC,IAAI,CAACL,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAqG,SAAS,CAACC,MAAM;IACd,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,CAACnG,IAAI,CAACiG,MAAM,CAAC;;IAEjC,IAAI,CAAClG,YAAY,CAACC,IAAI,CAACiG,MAAM,CAAC;EAChC;EAEA/H,wBAAwB,CAACF,GAAG;IAC1B,OAAO,EAAE,CAACA,GAAG,CAACmG,IAAI,IAAI,CAACnG,GAAG,CAACoI,UAAU,IAAI,CAACpI,GAAG,CAACqI,QAAQ,IAAI,CAACrI,GAAG,CAACpE,QAAQ,CAAC;EAC1E;EAEA8D,MAAM,CAACxE,MAAM,EAAEqI,MAAM,EAAEvD,GAAI;IACzB,IAAI,CAACsI,MAAM,CAACC,QAAQ,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG;MAClBC,UAAU,EAAE,IAAI,CAACnL,KAAK,CAAChC,IAAI;MAC3BoN,WAAW,EAAEnF;KACd;IACD;IACA,IAAI,CAACuD,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAA0B,IAAI;MAC5D,IAAIlH,YAAY,GAAGkH,UAAU,CAACjH,IAAI,CAAC;QAAE1E,QAAQ,EAAE;MAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;MAE7D,QAAQwD,MAAM;QACZ,KAAK,WAAW;UACd,IAAI,CAAC+E,MAAM,CAAC5N,KAAK,CAACG,IAAI,GAAG,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,YAAY,CAAC;UAErE,IAAI,CAAC2I,MAAM,GAAG,CAAC,GAAG,IAAI,CAACvH,SAAS,EAAE;YAChCX,GAAG,EAAE,cAAc;YACnBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE;cACLD,IAAI,EAAE;aACP;YACDa,YAAY,EAAE0D,YAAY,CAAC,CAAC,CAAC,EAAEuB;WAChC,CAAC,CAACf,GAAG,CAAEoF,IAAI,KAAM;YAAE,GAAGA;UAAI,CAAE,CAAC,CAAC;UAC/BzI,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACsG,MAAM,CAAC;UAClC,IAAI,CAAC2E,gBAAgB,GAAG,KAAK;UAE7B,IAAI,CAAC3E,MAAM,CAACpG,OAAO,CAAEsI,IAAI,IAAI;YAC3B,IAAIA,IAAI,CAACpK,GAAG,KAAK,SAAS,EAAE;cAC1BoK,IAAI,CAACpI,KAAK,GAAGwC,YAAY,CAAC,CAAC,CAAC,EAAE8I,QAAQ,EAAEtI,GAAG,CAAEuI,CAAC,IAAKA,CAAC,CAACrL,EAAE,CAAC,IAAI,EAAE;;UAElE,CAAC,CAAC;UAEF;QACF,KAAK,aAAa;UAChB,IAAI,CAAC+K,MAAM,CAAC5N,KAAK,CAACG,IAAI,GACpB,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;UAChD,IAAI,CAAC2I,MAAM,GAAG,IAAI,CAAC/F,WAAW;UAC9B,IAAI,CAAC0K,gBAAgB,GAAG,IAAI;UAC5B;QACF,KAAK,aAAa;UAChB,IAAI,CAACI,MAAM,CAAC5N,KAAK,CAACG,IAAI,GACpB,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,cAAc,CAAC;UAChD,IAAI,CAAC2I,MAAM,GAAG,IAAI,CAACsF,WAAW;UAC9B,IAAI,CAACX,gBAAgB,GAAG,KAAK;UAC7B;QACF,KAAK,iBAAiB;UACpBlL,OAAO,CAACC,GAAG,CAAC+C,GAAG,CAAC;UAEhB,IAAI8I,UAAU,GAAG;YACf,qBAAqB,EAAE9I,GAAG,CAACsG,UAAU;YACrC,sBAAsB,EAAEtG,GAAG,CAAC2G,WAAW;YACvC,mBAAmB,EAAE3G,GAAG,CAACzC,EAAE;YAC3B,uBAAuB,EAAEyC,GAAG,CAAC+I,YAAY,IAAI;WAC9C;UACD;UACA,IAAI,CAACP,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGM;UAAU,CAAE;UAC3D,IAAI,CAACR,MAAM,CAAC5N,KAAK,CAACG,IAAI,GACpB,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,mBAAmB,CAAC;UACrD,IAAI,CAAC2I,MAAM,GAAG,IAAI,CAACvH,SAAS;UAC5B,IAAI,CAACsM,MAAM,CAACC,QAAQ,GAAG,IAAI;UAC3B;QACF,KAAK,iBAAiB;UACpB,IAAI,CAACD,MAAM,CAAC5N,KAAK,CAACG,IAAI,GAAG,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;UACpE,IAAI,CAAC2I,MAAM,GAAG,IAAI,CAACtH,eAAe;UAClC,IAAI,CAACiM,gBAAgB,GAAG,KAAK;UAC7B;QACF;UACE,IAAI,CAACI,MAAM,CAAC5N,KAAK,CAACG,IAAI,GAAG,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;UACpE,IAAI,CAAC2I,MAAM,GAAG,IAAI,CAACvH,SAAS,CAACqE,GAAG,CAAEoF,IAAI,IAAI;YACxC,IAAIA,IAAI,CAACpK,GAAG,KAAK,SAAS,EAAE;cAC1BoK,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;cACzBA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;;YAEpB,OAAOA,IAAI;UACb,CAAC,CAAC;UACF,IAAI,CAACyC,gBAAgB,GAAG,KAAK;UAC7B;MAAM;MAEV,IAAI,CAACc,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC1F,MAAM,CAAC;MACrC,IAAI,CAAC+E,MAAM,CAACpN,MAAM,GAAGA,MAAM;MAC3B,IAAI,CAACoN,MAAM,CAACtI,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;MAElC,IAAI,CAAC5F,mBAAmB,CAAC8O,kBAAkB,CAAC,IAAI,CAACzO,UAAU,CAAC,CAAC0O,UAAU,EAAE;IAC3E,CAAC,CAAC;EACJ;EAEAC,cAAc,CAACC,KAAK;IAClB,IAAI,CAACrN,SAAS,CAACmB,OAAO,CAAE8B,CAAC,IAAI;MAC3B,IAAIA,CAAC,CAAC5D,GAAG,KAAK,SAAS,EAAE;QACvB4D,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE;QACtBA,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;;IAEnB,CAAC,CAAC;EACJ;EAEAqK,eAAe;IACbC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACC,SAAS,CAACP,IAAI,CAAC,IAAI,CAACxG,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAgH,WAAW;IACT,IAAI,CAACD,SAAS,CAACE,WAAW,EAAE;EAC9B;EA0BA5I,SAAS;IACP,IAAI,CAACgG,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAAgC,IAAI;MAClE,IAAIlH,YAAY,GAAGkH,UAAU,CAACjH,IAAI,CAAC;QAAE1E,QAAQ,EAAE;MAAI,CAAE,CAAC,CAAC2E,IAAI,EAAE;MAE7D,IAAI,CAAC4J,WAAW,GAAG,EAAE;MACrB9J,YAAY,CAACQ,GAAG,CAAEL,GAAG,IAAI;QACvB,OAAO,IAAI,CAAC2J,WAAW,CAACvM,IAAI,CAAC4C,GAAG,CAACzC,EAAE,CAAC;MACtC,CAAC,CAAC;MAEF,IAAIsC,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAC6I,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACC,kBAAkB,GAAG;UACxB,GAAG,IAAI,CAACA,kBAAkB;UAC1BC,aAAa,EAAE;SAChB;OACF,MAAM;QACL,IAAI,CAACF,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,kBAAkB,GAAG;UACxB,GAAG,IAAI,CAACA,kBAAkB;UAC1BC,aAAa,EAAEjK,YAAY,CAAC,CAAC,CAAC,EAAE8I,QAAQ,EAAEtI,GAAG,CAAEuI,CAAC,IAAKA,CAAC,CAACrL,EAAE,CAAC,IAAI;SAC/D;;MAGH,IAAI,CAAChD,aAAa,CAACwP,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;QAC/CC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAE,MAAK;UAClB,IAAI,CAACN,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACO,mBAAmB,CAACjN,OAAO,CAAEsI,IAAI,IAAI;YACxC,IAAIA,IAAI,CAACpK,GAAG,KAAK,eAAe,EAAE;cAChCoK,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;;UAE7B,CAAC,CAAC;UACF,OAAO,IAAI;QACb;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAhJ,cAAc;IACZ,IAAI,CAACxC,aAAa,CAACoQ,wBAAwB,CAAC,IAAI,CAAC/M,KAAK,CAACC,EAAE,CAAC,CAAC+F,SAAS,CAAEgH,QAAQ,IAAI;MAChF,IAAI,CAACC,YAAY,GAAGD,QAAQ,CAAC,MAAM,CAAC;MACpC,IAAI,CAACF,mBAAmB,CAACjN,OAAO,CAAEsI,IAAI,IAAI;QACxC,IAAIA,IAAI,CAACpK,GAAG,KAAK,eAAe,EAAE;UAChCoK,IAAI,CAAClK,KAAK,CAACJ,OAAO,GAAGmP,QAAQ,CAAC,MAAM,CAAC,CAACjK,GAAG,CAAEmK,OAAO,IAAI;YACpDxN,OAAO,CAACC,GAAG,CAACuN,OAAO,CAAC;YACpB,OAAO;cACLhP,KAAK,EAAEgP,OAAO,CAAC7E,IAAI,GACf,GAAG6E,OAAO,CAAC7E,IAAI,CAACC,UAAU,IAAI4E,OAAO,CAAC7E,IAAI,CAACE,SAAS,EAAE,GACtD2E,OAAO,CAAC1E,YAAY;cACxBzI,KAAK,EAAEmN,OAAO,CAACjN;aAChB;UACH,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CACA;EACH;EAEAkN,qBAAqB,CAACpB,KAAK;IACzB,IAAI,CAACvC,SAAS,CAACC,UAAU,CAACpG,IAAI,CAAEoG,UAA0B,IAAI;MAC5DA,UAAU,CAAClF,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA4I,YAAY;IACV,IAAI,CAACzQ,aAAa,CAAC0Q,YAAY,CAAC,IAAI,CAACrN,KAAK,CAACC,EAAE,CAAC,CAAC+F,SAAS,CAAEgH,QAAQ,IAAI;MACpEtN,OAAO,CAACC,GAAG,CAACqN,QAAQ,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC;qBAngDU3Q,qBAAqB;EAAA;EAAA;UAArBA,qBAAqB;IAAAiR;IAAAC;MAAA;uBACrBvR,kBAAkB;;;;;;;;;;;;;;;;;;;;;;QCxC/BI,8BAA+C;QAIvCA,qEAgBK;QACPA,iBAAM;QAENA,2BAKS;QACXA,iBAAM;QAIVA,uCAIC;QAKGA;UAAA,OAAaoR,qBAAiB;QAAA,EAAC;UAAA,OACpBA,0BAAsB;QAAA,EADF;QAKjCpR,iBAAqB;QAGvBA,uHAUc;;;QApDHA,eAAsD;QAAtDA,+EAAsD;QAiBzDA,eAAuB;QAAvBA,yCAAuB;QAU7BA,eAAmB;QAAnBA,qCAAmB;QAIjBA,eAAmB;QAAnBA,qCAAmB", "names": ["environment", "EventEmitter", "Subject", "AppConfig", "DataTableDirective", "<PERSON><PERSON>", "moment", "FormGroup", "i0", "StageMatchesComponent", "constructor", "_http", "_route", "_translateService", "_commonsService", "_stageService", "_userService", "_loadingService", "_coreSidebarService", "_toastr", "_exportService", "_modalService", "editor_id", "table_name", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "options", "selected", "key", "type", "props", "label", "placeholder", "max", "expressions", "location", "hideOnMultiple", "required", "cancelOptions", "editMatch", "updateRankMatch", "multiple", "defaultValue", "hooks", "ngOnInit", "loadInitSettings", "setUp", "buildTable", "getListReferee", "settings", "localStorage", "getItem", "initSettings", "JSON", "parse", "console", "log", "CANCEL_MATCH_TYPES", "for<PERSON>ach", "push", "value", "stage", "id", "updateScore", "min", "step", "hide", "TOURNAMENT_TYPES", "knockout", "sport_type", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "league", "groups", "i", "no_encounters", "roundLevelOpts", "splice", "roundLevelIndex", "findIndex", "field", "btns", "text", "e", "dt", "node", "config", "fire", "icon", "confirmButtonText", "customClass", "confirmButton", "editor", "attr", "autoGenerateMatches", "selectedRows", "rows", "data", "row", "validUpdate", "checkValidateUpdateScore", "extend", "ids", "map", "reverseButtons", "showCancelButton", "cancelButtonText", "buttonsStyling", "cancelButton", "then", "result", "resetScore", "openModal", "length", "x", "roundNameIndex", "hasCancel", "checkRowIsCancelled", "status", "home_team_id", "away_team_id", "swapTeams", "show", "deleteMatchesInStage", "to<PERSON>romise", "resp", "success", "ajax", "reload", "onDataChange", "emit", "button", "buttons", "exportData", "exportCsv", "tournament", "type_knockout", "KNOCKOUT_TYPES", "type4", "dtOptions", "dom", "dataTableDefaults", "select", "window", "innerWidth", "rowId", "rowGroup", "dataSrc", "order", "dataTablesParameters", "callback", "post", "subscribe", "fields", "teams", "rank_fields", "rank_label", "filterFriendlyMatches", "friendlyMatchesActive", "hasMatch", "recordsTotal", "recordsFiltered", "initComplete", "isComplete", "responsive", "scrollX", "language", "lang", "columnDefs", "responsivePriority", "targets", "columns", "visible", "render", "meta", "orderable", "className", "content", "format", "start_time_short", "end_time_short", "home_text", "home_label", "away_text", "away_label", "home_penalty", "away_penalty", "item", "<PERSON><PERSON><PERSON>", "user", "first_name", "last_name", "referee_name", "join", "now", "currentTime", "today", "date", "includes", "lengthMenu", "round_name", "split", "pop", "addOrRemoveRoundLevel", "is_friendly", "round_level", "round_level_index", "active", "dtElement", "dtInstance", "deselect", "column", "search", "draw", "enable", "disable", "_generateMatches", "error", "warning", "html", "replace", "message", "isConfirmed", "dismiss", "stage_match_id", "stage_match", "onSuccess", "$event", "is_score_updated", "onUpdateScore", "start_time", "end_time", "params", "use_data", "paramsToPost", "stage_type", "type_action", "referees", "r", "cancelMatch", "paramsPost", "match_number", "fields_subject", "next", "getSidebarRegistry", "toggle<PERSON><PERSON>", "onCloseSidebar", "event", "ngAfterViewInit", "setTimeout", "dtTrigger", "ngOnDestroy", "unsubscribe", "selectedIds", "isMultipleAssign", "assignRefereeModel", "list_referees", "open", "modalAssignReferee", "centered", "size", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getListRefereesByStageId", "response", "listReferees", "referee", "onSubmitAssignReferee", "autoschedule", "autoSchedule", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\stage-matches.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\stage-matches.component.html"], "sourcesContent": ["import { environment } from '../../../../../environments/environment';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n  TemplateRef,\r\n  ViewChild,\r\n  ViewEncapsulation\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { ExportService } from 'app/services/export.service';\r\nimport { Subject } from 'rxjs';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport Swal from 'sweetalert2';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport moment from 'moment';\r\nimport { StageMatch } from 'app/interfaces/stages';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { UserService } from '../../../../services/user.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { SeasonService } from '../../../../services/season.service';\r\n\r\n@Component({\r\n  selector: 'app-stage-matches',\r\n  templateUrl: './stage-matches.component.html',\r\n  styleUrls: ['./stage-matches.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class StageMatchesComponent implements OnInit {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @Input() stage: any;\r\n  public contentHeader: object;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  AppConfig = AppConfig;\r\n  @Input() tournament: any;\r\n  hasMatch: boolean = false;\r\n  private isComplete: boolean = false;\r\n  tournament_id: any;\r\n  @Output() onUpdateScore = new EventEmitter<any>();\r\n  @Output() onDataChange = new EventEmitter<any>();\r\n  initSettings: any;\r\n\r\n  listReferees = [];\r\n\r\n  // setup referee\r\n\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _route: ActivatedRoute,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _stageService: StageService,\r\n    public _userService: UserService,\r\n    public _loadingService: LoadingService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _toastr: ToastrService,\r\n    private _exportService: ExportService,\r\n    private _modalService: NgbModal\r\n  ) {\r\n  }\r\n\r\n  friendlyMatchesActive = false;\r\n  public fields_subject = new Subject<any>();\r\n  public cancelOptions = [];\r\n  public paramsToPost = {};\r\n  public is_score_updated = false;\r\n  public table_name = 'matches-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Add match'),\r\n      edit: this._translateService.instant('Edit match'),\r\n      remove: this._translateService.instant('Remove match')\r\n    },\r\n    url: `${environment.apiUrl}/stage-matches/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n  public location = {\r\n    options: [],\r\n    selected: null\r\n  };\r\n  public teams = {\r\n    options: [],\r\n    selected: null\r\n  };\r\n\r\n  roundLevelOpts = [];\r\n\r\n  public editMatch = [\r\n    {\r\n      key: 'date',\r\n      type: 'custom-date',\r\n      props: {\r\n        label: this._translateService.instant('Date'),\r\n        placeholder: this._translateService.instant('dd/mm/yyyy'),\r\n        // required: true,\r\n        max: '2100-12-31'\r\n      },\r\n      expressions: {\r\n        'props.required':\r\n          '(model.hasOwnProperty(\"start_time_short\") && model.start_time_short!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\r\n      }\r\n    },\r\n    {\r\n      key: 'start_time_short',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Start time'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter start time of match'\r\n        ),\r\n        // required: true,\r\n        type: 'time'\r\n      },\r\n      expressions: {\r\n        'props.required':\r\n          '(model.hasOwnProperty(\"date\") && model.date!=\"\") || (model.hasOwnProperty(\"end_time_short\") && model.end_time_short!=\"\")'\r\n      }\r\n    },\r\n    {\r\n      key: 'end_time_short',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('End time'),\r\n        placeholder: this._translateService.instant('Enter end time of match'),\r\n        // required: true,\r\n        type: 'time'\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'location_id',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Location'),\r\n        placeholder: this._translateService.instant('Select location'),\r\n        // required: true,\r\n        options: this.location.options\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'home_team_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Home team'),\r\n        placeholder: this._translateService.instant('Select home team'),\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'away_team_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Away team'),\r\n        placeholder: this._translateService.instant('Select away team'),\r\n        options: []\r\n      }\r\n    }\r\n  ];\r\n\r\n  public updateScore = [];\r\n\r\n  public cancelMatch = [\r\n    {\r\n      key: 'status',\r\n      type: 'radio',\r\n      props: {\r\n        label: this._translateService.instant('Cancel type'),\r\n        required: true,\r\n        options: this.cancelOptions\r\n      }\r\n    },\r\n    {\r\n      key: 'description',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Reason'),\r\n        placeholder: this._translateService.instant('Enter reason')\r\n      }\r\n    }\r\n  ];\r\n\r\n  public updateRankMatch = [\r\n    {\r\n      key: 'home_label_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Home label'),\r\n        placeholder: this._translateService.instant('Select home label'),\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'away_label_id',\r\n      type: 'select',\r\n      props: {\r\n        hideOnMultiple: true,\r\n        label: this._translateService.instant('Away label'),\r\n        placeholder: this._translateService.instant('Select away label'),\r\n        options: []\r\n      }\r\n    }\r\n  ];\r\n\r\n  public fields: any[] = this.editMatch;\r\n\r\n  public rank_fields: any[] = this.updateRankMatch;\r\n\r\n  ngOnInit(): void {\r\n    this.loadInitSettings();\r\n    this.setUp();\r\n    this.buildTable();\r\n    this.getListReferee();\r\n  }\r\n\r\n  loadInitSettings() {\r\n    const settings = localStorage.getItem('initSettings');\r\n    if (settings) {\r\n      this.initSettings = JSON.parse(settings);\r\n      console.log('initSettings:', this.initSettings);\r\n    } else {\r\n      console.log('No initSettings found in localStorage');\r\n    }\r\n  }\r\n\r\n  setUp() {\r\n    AppConfig.CANCEL_MATCH_TYPES.forEach((type) => {\r\n      this.cancelOptions.push({\r\n        value: type,\r\n        label: this._translateService.instant(type)\r\n      });\r\n    });\r\n    (this.editMatch as any).push({\r\n      key: 'stage_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      },\r\n      defaultValue: this.stage.id\r\n    });\r\n\r\n    this.updateScore = [\r\n      {\r\n        key: 'type',\r\n        type: 'input',\r\n        props: {\r\n          type: 'hidden'\r\n        },\r\n        defaultValue: this.stage.type\r\n      },\r\n      {\r\n        key: 'home_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Home score'),\r\n          required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'away_score',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Away score'),\r\n          required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 999,\r\n          step: 1\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'home_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Home penalty score'),\r\n          // required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        },\r\n        defaultValue: 0\r\n      },\r\n      {\r\n        key: 'away_penalty',\r\n        type: 'core-touchspin',\r\n        props: {\r\n          label: this._translateService.instant('Away penalty score'),\r\n          // required: true,\r\n          type: 'number',\r\n          min: 0,\r\n          max: 100,\r\n          step: 1\r\n        },\r\n        expressions: {\r\n          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != \"football\"`\r\n        },\r\n        defaultValue: 0\r\n      }\r\n    ];\r\n\r\n    this.contentHeader = {\r\n      headerTitle: this._translateService.instant('League Reports'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._translateService.instant('Leagues'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._translateService.instant('League Reports'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    if (\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.league ||\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.groups\r\n    ) {\r\n      // get round level options in stage\r\n      for (let i = 1; i <= this.stage.no_encounters; i++) {\r\n        this.roundLevelOpts.push({\r\n          value: `${i}`,\r\n          label: `${this._translateService.instant('Round')} ${i}`\r\n        });\r\n      }\r\n    }\r\n\r\n    if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      // add match type at beginning of editMatch\r\n      (this.editMatch as any).splice(0, 0, {\r\n        key: 'match_type',\r\n        type: 'select',\r\n        props: {\r\n          label: this._translateService.instant('Match type'),\r\n          required: true,\r\n          options: [\r\n            {\r\n              value: 1,\r\n              label: this._translateService.instant('League Match')\r\n            },\r\n            {\r\n              value: 2,\r\n              label: this._translateService.instant('Friendly Match')\r\n            }\r\n          ]\r\n        }\r\n      });\r\n    }\r\n\r\n    if (\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n    ) {\r\n      // find round level index in editMatch\r\n      let roundLevelIndex = (this.editMatch as any).findIndex(\r\n        (field: any) => field.key === 'round_level'\r\n      );\r\n      // remove round level in editMatch\r\n      if (roundLevelIndex > -1) {\r\n        (this.editMatch as any).splice(roundLevelIndex, 1);\r\n      }\r\n\r\n      // add round name at beginning of editMatch\r\n      (this.editMatch as any).splice(0, 0, {\r\n        key: 'round_name',\r\n        type: 'input',\r\n        props: {\r\n          hideOnMultiple: true,\r\n          label: this._translateService.instant('Match name'),\r\n          placeholder: this._translateService.instant('Enter round name')\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  buildTable() {\r\n    let btns = [\r\n      {\r\n        text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant(\r\n          'Add'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // check if stage type is league\r\n          if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not add match in this stage'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n\r\n          this.editor('create', 'editMatch');\r\n        }\r\n      },\r\n      {\r\n        attr: {\r\n          id: 'auto-generate-btn'\r\n        },\r\n        text: `<i class=\"fa-solid fa-wand-magic-sparkles\"></i> ${this._translateService.instant(\r\n          'Auto Generate'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          this.autoGenerateMatches();\r\n        }\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-edit\"></i> ${this._translateService.instant(\r\n          'Update Score'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          let row = selectedRows[0];\r\n          let validUpdate = false;\r\n          if (this.checkValidateUpdateScore(row)) {\r\n            validUpdate = true;\r\n          }\r\n          if (!validUpdate) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Cannot update score'),\r\n              text: this._translateService.instant(\r\n                'Please update the date, time and location of the match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n          } else {\r\n            this.editor('edit', 'updateScore');\r\n          }\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-rotate-ccw\"></i> ${this._translateService.instant(\r\n          'Reset Score'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n\r\n          // get stage_match id\r\n          let ids = [];\r\n\r\n          selectedRows.map((row) => ids.push(row.id));\r\n\r\n          // confirm reset score\r\n          Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(\r\n              'Are you sure you want to reset score this match(s)?'\r\n            ),\r\n            reverseButtons: true,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('No'),\r\n            buttonsStyling: false,\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary ml-1',\r\n              cancelButton: 'btn btn-outline-primary'\r\n            }\r\n          }).then((result) => {\r\n            if (result.value) {\r\n              this.resetScore(ids);\r\n            }\r\n          });\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-flag\"></i>${this._translateService.instant(\r\n          'Assign Referee'\r\n        )}`,\r\n        action: () => this.openModal(),\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-edit\"></i>${this._translateService.instant(\r\n          'Edit'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n\r\n          if (selectedRows.length > 1) {\r\n            // hide round level\r\n            let roundLevelIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_level'\r\n            );\r\n            if (roundLevelIndex > -1) {\r\n              (this.editMatch as any).splice(roundLevelIndex, 1);\r\n            }\r\n\r\n            let roundNameIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_name'\r\n            );\r\n\r\n            if (roundNameIndex > -1) {\r\n              (this.editMatch as any).splice(roundNameIndex, 1);\r\n            }\r\n          } else {\r\n            // show round level\r\n\r\n            let roundLevelIndex = (this.editMatch as any).findIndex(\r\n              (x) => x.key == 'round_level'\r\n            );\r\n            // if not found\r\n            if (\r\n              roundLevelIndex == -1 &&\r\n              this.stage.type == AppConfig.TOURNAMENT_TYPES.league\r\n            ) {\r\n              (this.editMatch as any).splice(3, 0, {\r\n                key: 'round_level',\r\n                type: 'select',\r\n                props: {\r\n                  label: this._translateService.instant('Round level'),\r\n                  placeholder:\r\n                    this._translateService.instant('Select round level'),\r\n                  // required: true,\r\n                  options: this.roundLevelOpts\r\n                }\r\n              });\r\n            }\r\n            if (\r\n              this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n            ) {\r\n              let roundNameIndex = (this.editMatch as any).findIndex(\r\n                (x) => x.key == 'round_name'\r\n              );\r\n\r\n              if (roundNameIndex == -1) {\r\n                (this.editMatch as any).splice(0, 0, {\r\n                  key: 'round_name',\r\n                  type: 'input',\r\n                  props: {\r\n                    label: this._translateService.instant('Match name'),\r\n                    placeholder:\r\n                      this._translateService.instant('Enter round name')\r\n                  }\r\n                });\r\n              }\r\n            }\r\n          }\r\n\r\n          this.editor('edit', 'editMatch');\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-repeat-1\"></i> ${this._translateService.instant(\r\n          'Add replace match'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          let row = selectedRows[0];\r\n          let hasCancel = false;\r\n          if (this.checkRowIsCancelled(row)) {\r\n            hasCancel = true;\r\n          }\r\n          if (!hasCancel) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not add replace match for this match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          this.editor('create', 'addReplaceMatch', row);\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-repeat\"></i> ${this._translateService.instant(\r\n          'Swap teams'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n\r\n          // check if home_team_id or away_team_id is null\r\n\r\n          if (selectedRows[0].status !== 'pass' && (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id)) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'You can not swap teams for this match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          } else {\r\n            // confirm swap teams\r\n            Swal.fire({\r\n              title: this._translateService.instant('Are you sure?'),\r\n              text: this._translateService.instant(\r\n                'Are you sure you want to swap teams in this match?'\r\n              ),\r\n              icon: 'warning',\r\n              reverseButtons: true,\r\n              showCancelButton: true,\r\n              confirmButtonText: this._translateService.instant('Yes'),\r\n              cancelButtonText: this._translateService.instant('No'),\r\n              buttonsStyling: false,\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary ml-1',\r\n                cancelButton: 'btn btn-outline-primary'\r\n              }\r\n            }).then((result) => {\r\n              if (result.value) {\r\n                this.swapTeams(selectedRows[0]);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-ban\"></i> ${this._translateService.instant(\r\n          'Cancel'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows has status is not can play\r\n          let hasCancel = false;\r\n          selectedRows.map((row) => {\r\n            if (this.checkRowIsCancelled(row)) {\r\n              hasCancel = true;\r\n            }\r\n          });\r\n\r\n          if (hasCancel) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Warning'),\r\n              text: this._translateService.instant(\r\n                'You can not cancel match that has been cancelled'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n\r\n            return;\r\n          }\r\n          // confirm cancel match\r\n          Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(\r\n              'Are you sure you want to cancel this match(s)?'\r\n            ),\r\n            reverseButtons: true,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('No'),\r\n            buttonsStyling: false,\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary ml-1',\r\n              cancelButton: 'btn btn-outline-primary'\r\n            }\r\n          }).then((result) => {\r\n            if (result.value) {\r\n              this.editor('edit', 'cancelMatch');\r\n            }\r\n          });\r\n        },\r\n\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: `<i class=\"feather icon-trash\"></i> ${this._translateService.instant(\r\n          'Delete'\r\n        )}`,\r\n        extend: 'selected',\r\n        action: (e, dt, node, config) => {\r\n          // get selected rows\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // get ids\r\n          let ids = [];\r\n          selectedRows.map((row) => {\r\n            ids.push(row.id);\r\n          });\r\n\r\n          // confirm delete\r\n          Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(\r\n              'You will not be able to recover this!'\r\n            ),\r\n            reverseButtons: true,\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('No'),\r\n            buttonsStyling: false,\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary ml-1',\r\n              cancelButton: 'btn btn-outline-primary'\r\n            }\r\n          }).then((result) => {\r\n            if (result.value) {\r\n              // delete\r\n              this._loadingService.show();\r\n              this._stageService\r\n                .deleteMatchesInStage(ids, this.stage.id)\r\n                .toPromise()\r\n                .then((resp) => {\r\n                  this._toastr.success(\r\n                    this._translateService.instant('Deleted successfully')\r\n                  );\r\n                  dt.ajax.reload();\r\n                  this.onDataChange.emit(resp);\r\n                });\r\n            }\r\n          });\r\n        }\r\n      },\r\n      {\r\n        text: `<i class=\"fas fa-file-export mr-1\"></i> ${this._translateService.instant(\r\n          'Export'\r\n        )}`,\r\n        extend: 'csv',\r\n        action: async (e: any, dt: any, button: any, config: any) => {\r\n          const data = dt.buttons.exportData();\r\n          await this._exportService.exportCsv(data, 'Matches.csv');\r\n        }\r\n      },\r\n      {\r\n        text: `<i class=\"fa-solid fa-code\"></i> ${this._translateService.instant(\r\n          'Define Team'\r\n        )}`,\r\n        action: (e, dt, node, config) => {\r\n          let selectedRows = dt.rows({ selected: true }).data();\r\n          // check if selected rows > 1\r\n          if (selectedRows.length > 1) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Notification'),\r\n              text: this._translateService.instant(\r\n                'Please select only one match'\r\n              ),\r\n              icon: 'warning',\r\n              confirmButtonText: this._translateService.instant('Ok'),\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary'\r\n              }\r\n            });\r\n            return;\r\n          }\r\n          this.editor('edit', 'updateRankMatch');\r\n        },\r\n        extend: 'selected'\r\n      },\r\n      {\r\n        text: this._translateService.instant('Columns'),\r\n        extend: 'colvis'\r\n      }\r\n    ];\r\n\r\n    // if stage type is league\r\n    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {\r\n      // remove first button\r\n      btns.splice(0, 1);\r\n    }\r\n\r\n    if (\r\n      this.stage.type != AppConfig.TOURNAMENT_TYPES.knockout ||\r\n      this.tournament.type_knockout === AppConfig.KNOCKOUT_TYPES.type4\r\n    ) {\r\n      btns.splice(-2, 1);\r\n    }\r\n\r\n    if (\r\n      this.tournament.type_knockout != AppConfig.KNOCKOUT_TYPES.type4 &&\r\n      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout\r\n    ) {\r\n      btns.splice(0, 1);\r\n      btns.splice(-4, 1);\r\n    }\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: window.innerWidth > 768 ? 'os' : 'multi',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      rowGroup: { dataSrc: 'group_round' },\r\n      order: [[2, 'asc']],\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // add season id\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/stage-matches/all-in-stage/${this.stage.id}`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            // find fields has key location_id and set options\r\n            this.fields.forEach((field) => {\r\n              if (field.key === 'location_id') {\r\n                field.props.options = resp.options.location;\r\n              }\r\n              if (field.key === 'home_team_id') {\r\n                field.props.options = resp.options.teams;\r\n              }\r\n              if (field.key === 'away_team_id') {\r\n                field.props.options = resp.options.teams;\r\n              }\r\n            });\r\n\r\n            this.rank_fields.forEach((field) => {\r\n              if (field.key === 'home_label_id') {\r\n                field.props.options = resp.options.rank_label;\r\n              }\r\n              if (field.key === 'away_label_id') {\r\n                field.props.options = resp.options.rank_label;\r\n              }\r\n            });\r\n\r\n            this.filterFriendlyMatches(this.friendlyMatchesActive);\r\n\r\n            this.hasMatch = resp.data.length > 0;\r\n\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      initComplete: () => {\r\n        this.isComplete = true;\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -7 },\r\n        { responsivePriority: 1, targets: -6 },\r\n        { responsivePriority: 1, targets: -5 },\r\n        { responsivePriority: 1, targets: -4 },\r\n        { responsivePriority: 1, targets: -3 },\r\n        { responsivePriority: 1, targets: -2 }\r\n      ],\r\n      columns: [\r\n        { data: 'order', visible: false },\r\n        { data: 'match_type', visible: false },\r\n        { data: 'group_round', visible: false },\r\n        {\r\n          title: this._translateService.instant('No'),\r\n          data: 'match_number',\r\n          visible: this.stage.type !== AppConfig.TOURNAMENT_TYPES.league,\r\n          render: function (data, type, row, meta) {\r\n            return data ? `${data}` : `${meta.row + 1}`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'date',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            let content = '';\r\n            if (!data || data === 'TBD') {\r\n              content = 'TBD';\r\n            } else {\r\n              // format to HH:mm from ISO 8601\r\n              content = moment(data).format('YYYY-MM-DD');\r\n            }\r\n            return `<p style=\"margin:0; min-width: max-content\">${content}</p>`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time_short',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            if (\r\n              !data ||\r\n              row.start_time_short == 'TBD' ||\r\n              !row.start_time_short\r\n            ) {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time_short',\r\n          orderable: false,\r\n          className: 'text-center p-1',\r\n          render: function (data, type, row) {\r\n            if (!data || row.end_time_short == 'TBD' || !row.end_time_short) {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location_name'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home team'),\r\n          data: 'home_team_name',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (data === 'TBD') {\r\n              if (row.home_text) {\r\n                return row.home_text;\r\n              } else if (row.home_label && row.home_label.label) {\r\n                return row.home_label.label;\r\n              }\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center p-0 font-weight-bolder',\r\n          render: function (data, type, row) {\r\n            return `VS`;\r\n          },\r\n          orderable: false\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away team'),\r\n          data: 'away_team_name',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (data === 'TBD') {\r\n              if (row.away_text) {\r\n                return row.away_text;\r\n              } else if (row.away_label && row.away_label.label) {\r\n                return row.away_label.label;\r\n              }\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (row.home_penalty != null) {\r\n              return `${data}<br> ( ${row.home_penalty} )`;\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center p-0',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            return ` - `;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center p-1',\r\n          orderable: false,\r\n          render: function (data, type, row) {\r\n            if (row.away_penalty != null) {\r\n              return `${data}<br> ( ${row.away_penalty} )`;\r\n            }\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Referees'),\r\n          visible: false,\r\n          data: 'referees',\r\n          render: (data) => {\r\n            return `<div class=\"d-flex flex-column gap-1\">\r\n              ${data.map((item) => {\r\n              const refereeName = item.user ? `${item.user.first_name} ${item.user.last_name}` : item.referee_name;\r\n              return `<p style=\"min-width: max-content; margin: 0\">${refereeName}</p>`;\r\n            }).join('')}\r\n            </div>`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Status'),\r\n          data: 'status',\r\n          render: (data: string, type, row) => {\r\n            const now = moment();\r\n            const currentTime = now.format('HH:mm');\r\n            const today = now.format('YYYY-MM-DD');\r\n            if (!data) {\r\n              // check if match time is over\r\n              if (row.end_time_short == 'TBD') {\r\n                return `<span class=\"badge badge-secondary text-capitalize\">TBD</span>`;\r\n              } else {\r\n                if (\r\n                  (row.date == today && row.end_time_short < currentTime) ||\r\n                  row.date < today\r\n                ) {\r\n                  return `<span class=\"badge badge-success text-capitalize\">${this._translateService.instant(\r\n                    'Finished'\r\n                  )}</span>`;\r\n                } else if (\r\n                  row.date == today &&\r\n                  row.start_time_short <= currentTime &&\r\n                  row.end_time_short >= currentTime\r\n                ) {\r\n                  return `<span class=\"badge badge-warning text-capitalize\">${this._translateService.instant(\r\n                    'In Progress'\r\n                  )}</span>`;\r\n                } else if (\r\n                  row.date > today ||\r\n                  (row.date == today && row.start_time_short > currentTime)\r\n                ) {\r\n                  return `<span class=\"badge badge-info text-capitalize\">${this._translateService.instant(\r\n                    'Upcoming'\r\n                  )}</span>`;\r\n                }\r\n              }\r\n            } else {\r\n              if (AppConfig.CANCEL_MATCH_TYPES.includes(data)) {\r\n                return `<span class=\"badge badge-danger text-capitalize\">${data}</span>`;\r\n              } else {\r\n                return `<span class=\"badge badge-secondary text-capitalize\">${data}</span>`;\r\n              }\r\n            }\r\n          },\r\n          className: 'text-center p-1',\r\n          orderable: false\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [25, 50, 100, -1],\r\n        [25, 50, 100, 'All']\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: btns\r\n      }\r\n    };\r\n\r\n    switch (this.stage.type) {\r\n      case AppConfig.TOURNAMENT_TYPES.knockout:\r\n        this.dtOptions.order = [[0, 'asc']];\r\n        // add rowGroup\r\n        this.dtOptions.rowGroup = { dataSrc: 'round_name' };\r\n        // insert round_name column at index 6\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data) {\r\n            return `<p style=\"margin:0; min-width: max-content\">${data}</p>`;\r\n          }\r\n        });\r\n\r\n        break;\r\n      case AppConfig.TOURNAMENT_TYPES.groups:\r\n        this.dtOptions.order = [[2, 'asc']];\r\n\r\n        // insert round_name column at index 5\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data, type, row) {\r\n            // split round_name by - and get the last item\r\n            if (!data) return '';\r\n            let round_name = data.split('-').pop();\r\n            return `<p style=\"margin:0; min-width: max-content\">${round_name}</p>`;\r\n          }\r\n        });\r\n        break;\r\n      case AppConfig.TOURNAMENT_TYPES.league:\r\n        // clear rowGroup\r\n        this.dtOptions.rowGroup = null;\r\n        this.dtOptions.columns.splice(7, 0, {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          className: 'text-center',\r\n          render: function (data) {\r\n            console.log('data', data);\r\n            return `<p style=\"margin:0; min-width: max-content\">${data ?? ''}</p>`;\r\n          }\r\n        });\r\n        // order by round_level\r\n        this.dtOptions.order = [[5, 'asc']];\r\n        break;\r\n\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  addOrRemoveRoundLevel(is_friendly: boolean) {\r\n    const round_level = {\r\n      key: 'round_level',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Round level'),\r\n        placeholder: this._translateService.instant('Select round level'),\r\n        // required: true,\r\n        options: this.roundLevelOpts\r\n      }\r\n    };\r\n\r\n    if (is_friendly) {\r\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\r\n        // remove round_level from editMatch\r\n        const round_level_index = (this.editMatch as any).findIndex(\r\n          (item) => item.key === 'round_level'\r\n        );\r\n        // check if round_level exist\r\n        if (round_level_index > -1) {\r\n          (this.editMatch as any).splice(round_level_index, 1);\r\n        }\r\n      }\r\n    } else {\r\n      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {\r\n        const round_level_index = (this.editMatch as any).findIndex(\r\n          (item) => item.key === 'round_level'\r\n        );\r\n        // check if round_level not exist\r\n        if (round_level_index < 0) {\r\n          (this.editMatch as any).splice(2, 0, {\r\n            ...round_level\r\n          });\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  filterFriendlyMatches(active) {\r\n    this.friendlyMatchesActive = active;\r\n    this.addOrRemoveRoundLevel(active);\r\n    // deselect all rows\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {\r\n      // round level backup\r\n      dtInstance.rows().deselect();\r\n      if (!active) {\r\n        // show column round name\r\n        dtInstance.column(5).visible(true);\r\n        dtInstance.column(1).search(1).draw();\r\n        // disable button auto generate\r\n        let btns = dtInstance.button('#auto-generate-btn');\r\n        console.log(btns);\r\n        if (btns) {\r\n          btns.enable();\r\n        }\r\n      } else {\r\n        // hide column round name\r\n        dtInstance.column(5).visible(true);\r\n        dtInstance.column(1).search(2).draw();\r\n        // enable button auto generate\r\n        let btns = dtInstance.button('#auto-generate-btn');\r\n        console.log(btns);\r\n        if (btns) {\r\n          btns.disable();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  checkRowIsCancelled(row) {\r\n    let hasCancel = false;\r\n    // if row has status in AppConfig.CANCEL_MATCH_TYPES\r\n    if (AppConfig.CANCEL_MATCH_TYPES.includes(row.status)) {\r\n      hasCancel = true;\r\n    }\r\n    return hasCancel;\r\n  }\r\n\r\n  private _generateMatches() {\r\n    this._stageService.autoGenerateMatches(this.stage.id).subscribe(\r\n      (resp: any) => {\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n          this.onDataChange.emit(resp);\r\n        });\r\n      },\r\n      (error) => {\r\n        if (error.warning) {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Cannot Auto Generate'),\r\n            html: error.warning.replace(/\\n/g, '<br>'),\r\n            icon: 'warning',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary'\r\n            }\r\n          });\r\n          return;\r\n        } else {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Error'),\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary'\r\n            }\r\n          });\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  autoGenerateMatches() {\r\n    console.log('auto generate matches', this.hasMatch);\r\n    // check if matches not null\r\n    if (this.hasMatch) {\r\n      this._loadingService.show();\r\n\r\n      Swal.fire({\r\n        title: `${this._translateService.instant('Are you sure?')}`,\r\n        html: `\r\n                <p>${this._translateService.instant(\r\n          'All current matches will be deleted unless they contain information'\r\n        )}.</p>\r\n                <div class=\"swal2-checkbox-container\" style=\"display:flex;justify-content:center;gap: 10px;\">\r\n                    <input type=\"checkbox\" id=\"confirmCheckbox\" >\r\n                    <label for=\"confirmCheckbox\" class=\"swal2-label\">${this._translateService.instant(\r\n          'I confirm that I want to generate all current matches'\r\n        )}</label>\r\n                    <div class=\"text-danger swal2-label\" id=\"swal2-validation-message\" style=\"display: none;\"></div>\r\n                </div>\r\n            `,\r\n        icon: 'warning',\r\n        showCancelButton: true,\r\n        confirmButtonText: this._translateService.instant('Yes'),\r\n        cancelButtonText: this._translateService.instant('No')\r\n      }).then((result) => {\r\n        if (result.isConfirmed) {\r\n          this._generateMatches();\r\n        } else {\r\n          this._loadingService.dismiss();\r\n        }\r\n      });\r\n    } else {\r\n      if (this.isComplete) {\r\n        this._generateMatches();\r\n      }\r\n    }\r\n  }\r\n\r\n  resetScore(stage_match_id) {\r\n    console.log(`reset score for ${stage_match_id}`);\r\n    this._loadingService.show();\r\n    this._stageService.resetScore(stage_match_id).subscribe(\r\n      (resp: any) => {\r\n        console.log(resp);\r\n\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n          this.onDataChange.emit(resp);\r\n        });\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: this._translateService.instant('Error'),\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary'\r\n          }\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  swapTeams(stage_match: StageMatch) {\r\n    this._loadingService.show();\r\n    this._stageService.swapTeams(stage_match).subscribe((resp: any) => {\r\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        dtInstance.ajax.reload();\r\n        this.onDataChange.emit(resp);\r\n      });\r\n    });\r\n  }\r\n\r\n  onSuccess($event) {\r\n    if (this.is_score_updated) {\r\n      this.onUpdateScore.emit($event);\r\n    }\r\n    this.onDataChange.emit($event);\r\n  }\r\n\r\n  checkValidateUpdateScore(row) {\r\n    return !(!row.date || !row.start_time || !row.end_time || !row.location);\r\n  }\r\n\r\n  editor(action, fields, row?) {\r\n    this.params.use_data = false;\r\n    this.paramsToPost = {\r\n      stage_type: this.stage.type,\r\n      type_action: fields\r\n    };\r\n    // check has row with status is pass in selected rows\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      let selectedRows = dtInstance.rows({ selected: true }).data();\r\n\r\n      switch (fields) {\r\n        case 'editMatch':\r\n          this.params.title.edit = this._translateService.instant('Edit match');\r\n\r\n          this.fields = [...this.editMatch, {\r\n            key: 'match_status',\r\n            type: 'input',\r\n            props: {\r\n              type: 'hidden'\r\n            },\r\n            defaultValue: selectedRows[0]?.status\r\n          }].map((item) => ({ ...item }));\r\n          console.log('fields', this.fields);\r\n          this.is_score_updated = false;\r\n\r\n          this.fields.forEach((item) => {\r\n            if (item.key === 'referee') {\r\n              item.value = selectedRows[0]?.referees?.map((r) => r.id) || [];\r\n            }\r\n          });\r\n\r\n          break;\r\n        case 'updateScore':\r\n          this.params.title.edit =\r\n            this._translateService.instant('Update Score');\r\n          this.fields = this.updateScore;\r\n          this.is_score_updated = true;\r\n          break;\r\n        case 'cancelMatch':\r\n          this.params.title.edit =\r\n            this._translateService.instant('Cancel Match');\r\n          this.fields = this.cancelMatch;\r\n          this.is_score_updated = false;\r\n          break;\r\n        case 'addReplaceMatch':\r\n          console.log(row);\r\n\r\n          let paramsPost = {\r\n            'data[0][round_name]': row.round_name,\r\n            'data[0][round_level]': row.round_level,\r\n            'data[0][match_id]': row.id,\r\n            'data[0][match_number]': row.match_number || 0\r\n          };\r\n          // merge paramsToPost and paramsPost\r\n          this.paramsToPost = { ...this.paramsToPost, ...paramsPost };\r\n          this.params.title.edit =\r\n            this._translateService.instant('Add Replace Match');\r\n          this.fields = this.editMatch;\r\n          this.params.use_data = true;\r\n          break;\r\n        case 'updateRankMatch':\r\n          this.params.title.edit = this._translateService.instant('Edit Rank');\r\n          this.fields = this.updateRankMatch;\r\n          this.is_score_updated = false;\r\n          break;\r\n        default:\r\n          this.params.title.edit = this._translateService.instant('Add Match');\r\n          this.fields = this.editMatch.map((item) => {\r\n            if (item.key === 'referee') {\r\n              item['defaultValue'] = [];\r\n              item['value'] = [];\r\n            }\r\n            return item;\r\n          });\r\n          this.is_score_updated = false;\r\n          break;\r\n      }\r\n      this.fields_subject.next(this.fields);\r\n      this.params.action = action;\r\n      this.params.row = row ? row : null;\r\n\r\n      this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n    });\r\n  }\r\n\r\n  onCloseSidebar(event) {\r\n    this.editMatch.forEach((e) => {\r\n      if (e.key === 'referee') {\r\n        e['defaultValue'] = [];\r\n        e['value'] = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\r\n      this.dtTrigger.next(this.dtOptions);\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n\r\n  @ViewChild('modalAssignReferee')\r\n  modalAssignReferee!: TemplateRef<any>;\r\n\r\n  public isMultipleAssign = true;\r\n  public selectedIds = [];\r\n  public assignRefereeForm = new FormGroup({});\r\n  public assignRefereeModel = {};\r\n  public assignRefereeFields = [\r\n    {\r\n      key: 'list_referees',\r\n      type: 'ng-select',\r\n      props: {\r\n        multiple: true,\r\n        hideOnMultiple: true,\r\n        defaultValue: [],\r\n        label: this._translateService.instant('Match Referee'),\r\n        placeholder: this._translateService.instant('Select match referees'),\r\n        options: []\r\n      },\r\n      hooks: {}\r\n    }\r\n  ];\r\n\r\n\r\n  openModal() {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {\r\n      let selectedRows = dtInstance.rows({ selected: true }).data();\r\n\r\n      this.selectedIds = [];\r\n      selectedRows.map((row) => {\r\n        return this.selectedIds.push(row.id);\r\n      });\r\n\r\n      if (selectedRows.length > 1) {\r\n        this.isMultipleAssign = true;\r\n        this.assignRefereeModel = {\r\n          ...this.assignRefereeModel,\r\n          list_referees: []\r\n        };\r\n      } else {\r\n        this.isMultipleAssign = false;\r\n        this.assignRefereeModel = {\r\n          ...this.assignRefereeModel,\r\n          list_referees: selectedRows[0]?.referees?.map((r) => r.id) || []\r\n        };\r\n      }\r\n\r\n      this._modalService.open(this.modalAssignReferee, {\r\n        centered: true,\r\n        size: 'lg',\r\n        beforeDismiss: () => {\r\n          this.assignRefereeModel = {};\r\n          this.assignRefereeFields.forEach((item) => {\r\n            if (item.key === 'list_referees') {\r\n              item['defaultValue'] = [];\r\n            }\r\n          });\r\n          return true;\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  getListReferee() {\r\n    this._stageService.getListRefereesByStageId(this.stage.id).subscribe((response) => {\r\n      this.listReferees = response['data'];\r\n      this.assignRefereeFields.forEach((item) => {\r\n        if (item.key === 'list_referees') {\r\n          item.props.options = response['data'].map((referee) => {\r\n            console.log(referee);\r\n            return {\r\n              label: referee.user\r\n                ? `${referee.user.first_name} ${referee.user.last_name}`\r\n                : referee.referee_name,\r\n              value: referee.id\r\n            };\r\n          });\r\n        }\r\n      });\r\n    }\r\n    );\r\n  }\r\n\r\n  onSubmitAssignReferee(event) {\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  autoschedule() {\r\n    this._stageService.autoSchedule(this.stage.id).subscribe((response) => {\r\n      console.log(response);\r\n    })\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header px-0 pt-0\">\r\n        <ul\r\n          ngbNav\r\n          #nav=\"ngbNav\"\r\n          class=\"nav-tabs\"\r\n          *ngIf=\"stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n        >\r\n          <li ngbNavItem>\r\n            <a ngbNavLink (click)=\"filterFriendlyMatches(false)\">{{\r\n                'All Matches' | translate\r\n              }}</a>\r\n          </li>\r\n          <li ngbNavItem>\r\n            <a ngbNavLink (click)=\"filterFriendlyMatches(true)\">{{\r\n                'Friendly Matches' | translate\r\n              }}</a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        [dtTrigger]=\"dtTrigger\"\r\n        class=\"table row-border hover\"\r\n      ></table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"table_name\"\r\n  overlayClass=\"modal-backdrop\"\r\n>\r\n  <app-editor-sidebar\r\n    [table]=\"dtElement\"\r\n    [fields]=\"fields\"\r\n    [params]=\"params\"\r\n    (onSuccess)=\"onSuccess($event)\"\r\n    (onClose)=\"onCloseSidebar($event)\"\r\n    [paramsToPost]=\"paramsToPost\"\r\n    [fields_subject]=\"fields_subject\"\r\n  >\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #modalAssignReferee let-modal>\r\n  <app-modal-assign-referees\r\n    [isMultipleAssign]=\"isMultipleAssign\"\r\n    [selectedIds]=\"selectedIds\"\r\n    [listReferees]=\"listReferees\"\r\n    [assignRefereeForm]=\"assignRefereeForm\"\r\n    [assignRefereeFields]=\"assignRefereeFields\"\r\n    [assignRefereeModel]=\"assignRefereeModel\"\r\n    (onSubmit)=\"onSubmitAssignReferee($event)\"\r\n  />\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}