import { LOCALE_ID, NgModule } from '@angular/core';

import { FilterPipe } from '@core/pipes/filter.pipe';

import { InitialsPipe } from '@core/pipes/initials.pipe';
import { SafePipe } from '@core/pipes/safe.pipe';
import { StripHtmlPipe } from '@core/pipes/stripHtml.pipe';
import { DotStringPipe } from '@core/pipes/dotstring.pipe';
import { LocalizedDatePipe } from './localized-date.pipe';
import { registerLocaleData } from '@angular/common';
import localeZhHantHK from '@angular/common/locales/zh-Hant-HK';
import localeVi from '@angular/common/locales/vi';
import localeEnGB from '@angular/common/locales/en-GB';
import { SortByNamePipe } from './sort-by-name.pipe';
import { DecodeHtmlPipe } from './decode-html.pipe';
registerLocaleData(localeZhHantHK);
registerLocaleData(localeVi);
registerLocaleData(localeEnGB);
@NgModule({
  declarations: [
    InitialsPipe,
    FilterPipe,
    StripHtmlPipe,
    SafePipe,
    DotStringPipe,
    LocalizedDatePipe,
    SortByNamePipe,
    DecodeHtmlPipe
  ],
  imports: [],
  exports: [
    InitialsPipe,
    FilterPipe,
    StripHtmlPipe,
    SafePipe,
    DotStringPipe,
    LocalizedDatePipe,
    SortByNamePipe,
    DecodeHtmlPipe
  ],
})
export class CorePipesModule { }
