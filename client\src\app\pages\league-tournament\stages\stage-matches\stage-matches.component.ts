import { environment } from '../../../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { CommonsService } from 'app/services/commons.service';
import { ExportService } from 'app/services/export.service';
import { Subject } from 'rxjs';
import { AppConfig } from 'app/app-config';
import { StageService } from 'app/services/stage.service';
import { DataTableDirective } from 'angular-datatables';
import Swal from 'sweetalert2';
import { LoadingService } from 'app/services/loading.service';
import { EditorSidebarParams } from 'app/interfaces/editor-sidebar';
import { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { StageMatch } from 'app/interfaces/stages';
import { ActivatedRoute } from '@angular/router';
import { UserService } from '../../../../services/user.service';
import { FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SeasonService } from '../../../../services/season.service';

@Component({
  selector: 'app-stage-matches',
  templateUrl: './stage-matches.component.html',
  styleUrls: ['./stage-matches.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class StageMatchesComponent implements OnInit {
  @ViewChild(DataTableDirective, { static: false })
  dtElement: any = DataTableDirective;
  @Input() stage: any;
  public contentHeader: object;
  dtOptions: any = {};
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
  AppConfig = AppConfig;
  @Input() tournament: any;
  hasMatch: boolean = false;
  private isComplete: boolean = false;
  tournament_id: any;
  @Output() onUpdateScore = new EventEmitter<any>();
  @Output() onDataChange = new EventEmitter<any>();
  initSettings: any;

  listReferees = [];

  // setup referee

  constructor(
    private _http: HttpClient,
    public _route: ActivatedRoute,
    public _translateService: TranslateService,
    public _commonsService: CommonsService,
    public _stageService: StageService,
    public _userService: UserService,
    public _loadingService: LoadingService,
    public _coreSidebarService: CoreSidebarService,
    public _toastr: ToastrService,
    private _exportService: ExportService,
    private _modalService: NgbModal
  ) {
  }

  friendlyMatchesActive = false;
  public fields_subject = new Subject<any>();
  public cancelOptions = [];
  public paramsToPost = {};
  public is_score_updated = false;
  public table_name = 'matches-table';
  public params: EditorSidebarParams = {
    editor_id: this.table_name,
    title: {
      create: this._translateService.instant('Add match'),
      edit: this._translateService.instant('Edit match'),
      remove: this._translateService.instant('Remove match')
    },
    url: `${environment.apiUrl}/stage-matches/editor`,
    method: 'POST',
    action: 'create'
  };
  public location = {
    options: [],
    selected: null
  };
  public teams = {
    options: [],
    selected: null
  };

  roundLevelOpts = [];

  public editMatch = [
    {
      key: 'date',
      type: 'custom-date',
      props: {
        label: this._translateService.instant('Date'),
        placeholder: this._translateService.instant('dd/mm/yyyy'),
        // required: true,
        max: '2100-12-31'
      },
      expressions: {
        'props.required':
          '(model.hasOwnProperty("start_time_short") && model.start_time_short!="") || (model.hasOwnProperty("end_time_short") && model.end_time_short!="")'
      }
    },
    {
      key: 'start_time_short',
      type: 'input',
      props: {
        label: this._translateService.instant('Start time'),
        placeholder: this._translateService.instant(
          'Enter start time of match'
        ),
        // required: true,
        type: 'time'
      },
      expressions: {
        'props.required':
          '(model.hasOwnProperty("date") && model.date!="") || (model.hasOwnProperty("end_time_short") && model.end_time_short!="")'
      }
    },
    {
      key: 'end_time_short',
      type: 'input',
      props: {
        label: this._translateService.instant('End time'),
        placeholder: this._translateService.instant('Enter end time of match'),
        // required: true,
        type: 'time'
      }
    },

    {
      key: 'location_id',
      type: 'select',
      props: {
        label: this._translateService.instant('Location'),
        placeholder: this._translateService.instant('Select location'),
        // required: true,
        options: this.location.options
      }
    },

    {
      key: 'home_team_id',
      type: 'select',
      props: {
        hideOnMultiple: true,
        label: this._translateService.instant('Home team'),
        placeholder: this._translateService.instant('Select home team'),
        options: []
      }
    },
    {
      key: 'away_team_id',
      type: 'select',
      props: {
        hideOnMultiple: true,
        label: this._translateService.instant('Away team'),
        placeholder: this._translateService.instant('Select away team'),
        options: []
      }
    }
  ];

  public updateScore = [];

  public cancelMatch = [
    {
      key: 'status',
      type: 'radio',
      props: {
        label: this._translateService.instant('Cancel type'),
        required: true,
        options: this.cancelOptions
      }
    },
    {
      key: 'description',
      type: 'input',
      props: {
        label: this._translateService.instant('Reason'),
        placeholder: this._translateService.instant('Enter reason')
      }
    }
  ];

  public updateRankMatch = [
    {
      key: 'home_label_id',
      type: 'select',
      props: {
        hideOnMultiple: true,
        label: this._translateService.instant('Home label'),
        placeholder: this._translateService.instant('Select home label'),
        options: []
      }
    },
    {
      key: 'away_label_id',
      type: 'select',
      props: {
        hideOnMultiple: true,
        label: this._translateService.instant('Away label'),
        placeholder: this._translateService.instant('Select away label'),
        options: []
      }
    }
  ];

  public fields: any[] = this.editMatch;

  public rank_fields: any[] = this.updateRankMatch;

  ngOnInit(): void {
    this.loadInitSettings();
    this.setUp();
    this.buildTable();
    this.getListReferee();
  }

  loadInitSettings() {
    const settings = localStorage.getItem('initSettings');
    if (settings) {
      this.initSettings = JSON.parse(settings);
      console.log('initSettings:', this.initSettings);
    } else {
      console.log('No initSettings found in localStorage');
    }
  }

  setUp() {
    AppConfig.CANCEL_MATCH_TYPES.forEach((type) => {
      this.cancelOptions.push({
        value: type,
        label: this._translateService.instant(type)
      });
    });
    (this.editMatch as any).push({
      key: 'stage_id',
      type: 'input',
      props: {
        type: 'hidden'
      },
      defaultValue: this.stage.id
    });

    this.updateScore = [
      {
        key: 'type',
        type: 'input',
        props: {
          type: 'hidden'
        },
        defaultValue: this.stage.type
      },
      {
        key: 'home_score',
        type: 'core-touchspin',
        props: {
          label: this._translateService.instant('Home score'),
          required: true,
          type: 'number',
          min: 0,
          max: 999,
          step: 1
        },
        defaultValue: 0
      },
      {
        key: 'away_score',
        type: 'core-touchspin',
        props: {
          label: this._translateService.instant('Away score'),
          required: true,
          type: 'number',
          min: 0,
          max: 999,
          step: 1
        },
        defaultValue: 0
      },
      {
        key: 'home_penalty',
        type: 'core-touchspin',
        props: {
          label: this._translateService.instant('Home penalty score'),
          // required: true,
          type: 'number',
          min: 0,
          max: 100,
          step: 1
        },
        expressions: {
          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != "football"`
        },
        defaultValue: 0
      },
      {
        key: 'away_penalty',
        type: 'core-touchspin',
        props: {
          label: this._translateService.instant('Away penalty score'),
          // required: true,
          type: 'number',
          min: 0,
          max: 100,
          step: 1
        },
        expressions: {
          hide: `model.type != '${AppConfig.TOURNAMENT_TYPES.knockout}' || model.home_score != model.away_score || '${this.initSettings.sport_type}' != "football"`
        },
        defaultValue: 0
      }
    ];

    this.contentHeader = {
      headerTitle: this._translateService.instant('League Reports'),
      actionButton: false,
      breadcrumb: {
        type: '',
        links: [
          {
            name: this._translateService.instant('Leagues'),
            isLink: false
          },
          {
            name: this._translateService.instant('League Reports'),
            isLink: false
          }
        ]
      }
    };

    if (
      this.stage.type === AppConfig.TOURNAMENT_TYPES.league ||
      this.stage.type === AppConfig.TOURNAMENT_TYPES.groups
    ) {
      // get round level options in stage
      for (let i = 1; i <= this.stage.no_encounters; i++) {
        this.roundLevelOpts.push({
          value: `${i}`,
          label: `${this._translateService.instant('Round')} ${i}`
        });
      }
    }

    if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {
      // add match type at beginning of editMatch
      (this.editMatch as any).splice(0, 0, {
        key: 'match_type',
        type: 'select',
        props: {
          label: this._translateService.instant('Match type'),
          required: true,
          options: [
            {
              value: 1,
              label: this._translateService.instant('League Match')
            },
            {
              value: 2,
              label: this._translateService.instant('Friendly Match')
            }
          ]
        }
      });
    }

    if (
      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout
    ) {
      // find round level index in editMatch
      let roundLevelIndex = (this.editMatch as any).findIndex(
        (field: any) => field.key === 'round_level'
      );
      // remove round level in editMatch
      if (roundLevelIndex > -1) {
        (this.editMatch as any).splice(roundLevelIndex, 1);
      }

      // add round name at beginning of editMatch
      (this.editMatch as any).splice(0, 0, {
        key: 'round_name',
        type: 'input',
        props: {
          hideOnMultiple: true,
          label: this._translateService.instant('Match name'),
          placeholder: this._translateService.instant('Enter round name')
        }
      });
    }
  }

  buildTable() {
    let btns = [
      {
        text: `<i class="fa-solid fa-plus"></i> ${this._translateService.instant(
          'Add'
        )}`,
        action: (e, dt, node, config) => {
          // check if stage type is league
          if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'You can not add match in this stage'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          }

          this.editor('create', 'editMatch');
        }
      },
      {
        attr: {
          id: 'auto-generate-btn'
        },
        text: `<i class="fa-solid fa-wand-magic-sparkles"></i> ${this._translateService.instant(
          'Auto Generate'
        )}`,
        action: (e, dt, node, config) => {
          this.autoGenerateMatches();
        }
      },
      {
        text: `<i class="feather icon-edit"></i> ${this._translateService.instant(
          'Update Score'
        )}`,
        action: (e, dt, node, config) => {
          let selectedRows = dt.rows({ selected: true }).data();
          let row = selectedRows[0];
          let validUpdate = false;
          if (this.checkValidateUpdateScore(row)) {
            validUpdate = true;
          }
          if (!validUpdate) {
            Swal.fire({
              title: this._translateService.instant('Cannot update score'),
              text: this._translateService.instant(
                'Please update the date, time and location of the match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
          } else {
            this.editor('edit', 'updateScore');
          }
        },
        extend: 'selected'
      },
      {
        text: `<i class="feather icon-rotate-ccw"></i> ${this._translateService.instant(
          'Reset Score'
        )}`,
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();

          // get stage_match id
          let ids = [];

          selectedRows.map((row) => ids.push(row.id));

          // confirm reset score
          Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant(
              'Are you sure you want to reset score this match(s)?'
            ),
            reverseButtons: true,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: this._translateService.instant('Yes'),
            cancelButtonText: this._translateService.instant('No'),
            buttonsStyling: false,
            customClass: {
              confirmButton: 'btn btn-primary ml-1',
              cancelButton: 'btn btn-outline-primary'
            }
          }).then((result) => {
            if (result.value) {
              this.resetScore(ids);
            }
          });
        },
        extend: 'selected'
      },
      {
        text: `<i class="feather icon-flag"></i>${this._translateService.instant(
          'Assign Referee'
        )}`,
        action: () => this.openModal(),
        extend: 'selected'
      },
      {
        text: `<i class="feather icon-edit"></i>${this._translateService.instant(
          'Edit'
        )}`,
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();

          if (selectedRows.length > 1) {
            // hide round level
            let roundLevelIndex = (this.editMatch as any).findIndex(
              (x) => x.key == 'round_level'
            );
            if (roundLevelIndex > -1) {
              (this.editMatch as any).splice(roundLevelIndex, 1);
            }

            let roundNameIndex = (this.editMatch as any).findIndex(
              (x) => x.key == 'round_name'
            );

            if (roundNameIndex > -1) {
              (this.editMatch as any).splice(roundNameIndex, 1);
            }
          } else {
            // show round level

            let roundLevelIndex = (this.editMatch as any).findIndex(
              (x) => x.key == 'round_level'
            );
            // if not found
            if (
              roundLevelIndex == -1 &&
              this.stage.type == AppConfig.TOURNAMENT_TYPES.league
            ) {
              (this.editMatch as any).splice(3, 0, {
                key: 'round_level',
                type: 'select',
                props: {
                  label: this._translateService.instant('Round level'),
                  placeholder:
                    this._translateService.instant('Select round level'),
                  // required: true,
                  options: this.roundLevelOpts
                }
              });
            }
            if (
              this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout
            ) {
              let roundNameIndex = (this.editMatch as any).findIndex(
                (x) => x.key == 'round_name'
              );

              if (roundNameIndex == -1) {
                (this.editMatch as any).splice(0, 0, {
                  key: 'round_name',
                  type: 'input',
                  props: {
                    label: this._translateService.instant('Match name'),
                    placeholder:
                      this._translateService.instant('Enter round name')
                  }
                });
              }
            }
          }

          this.editor('edit', 'editMatch');
        },
        extend: 'selected'
      },
      {
        text: `<i class="fa-solid fa-repeat-1"></i> ${this._translateService.instant(
          'Add replace match'
        )}`,
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();
          // check if selected rows > 1
          if (selectedRows.length > 1) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'Please select only one match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          }
          let row = selectedRows[0];
          let hasCancel = false;
          if (this.checkRowIsCancelled(row)) {
            hasCancel = true;
          }
          if (!hasCancel) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'You can not add replace match for this match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          }
          this.editor('create', 'addReplaceMatch', row);
        },
        extend: 'selected'
      },
      {
        text: `<i class="feather icon-repeat"></i> ${this._translateService.instant(
          'Swap teams'
        )}`,
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();
          // check if selected rows > 1
          if (selectedRows.length > 1) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'Please select only one match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          }

          // check if home_team_id or away_team_id is null

          if (selectedRows[0].status !== 'pass' && (!selectedRows[0].home_team_id || !selectedRows[0].away_team_id)) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'You can not swap teams for this match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          } else {
            // confirm swap teams
            Swal.fire({
              title: this._translateService.instant('Are you sure?'),
              text: this._translateService.instant(
                'Are you sure you want to swap teams in this match?'
              ),
              icon: 'warning',
              reverseButtons: true,
              showCancelButton: true,
              confirmButtonText: this._translateService.instant('Yes'),
              cancelButtonText: this._translateService.instant('No'),
              buttonsStyling: false,
              customClass: {
                confirmButton: 'btn btn-primary ml-1',
                cancelButton: 'btn btn-outline-primary'
              }
            }).then((result) => {
              if (result.value) {
                this.swapTeams(selectedRows[0]);
              }
            });
          }
        },
        extend: 'selected'
      },
      {
        text: `<i class="fa-solid fa-ban"></i> ${this._translateService.instant(
          'Cancel'
        )}`,
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();
          // check if selected rows has status is not can play
          let hasCancel = false;
          selectedRows.map((row) => {
            if (this.checkRowIsCancelled(row)) {
              hasCancel = true;
            }
          });

          if (hasCancel) {
            Swal.fire({
              title: this._translateService.instant('Warning'),
              text: this._translateService.instant(
                'You can not cancel match that has been cancelled'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('OK'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });

            return;
          }
          // confirm cancel match
          Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant(
              'Are you sure you want to cancel this match(s)?'
            ),
            reverseButtons: true,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: this._translateService.instant('Yes'),
            cancelButtonText: this._translateService.instant('No'),
            buttonsStyling: false,
            customClass: {
              confirmButton: 'btn btn-primary ml-1',
              cancelButton: 'btn btn-outline-primary'
            }
          }).then((result) => {
            if (result.value) {
              this.editor('edit', 'cancelMatch');
            }
          });
        },

        extend: 'selected'
      },
      {
        text: `<i class="feather icon-trash"></i> ${this._translateService.instant(
          'Delete'
        )}`,
        extend: 'selected',
        action: (e, dt, node, config) => {
          // get selected rows
          let selectedRows = dt.rows({ selected: true }).data();
          // get ids
          let ids = [];
          selectedRows.map((row) => {
            ids.push(row.id);
          });

          // confirm delete
          Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant(
              'You will not be able to recover this!'
            ),
            reverseButtons: true,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: this._translateService.instant('Yes'),
            cancelButtonText: this._translateService.instant('No'),
            buttonsStyling: false,
            customClass: {
              confirmButton: 'btn btn-primary ml-1',
              cancelButton: 'btn btn-outline-primary'
            }
          }).then((result) => {
            if (result.value) {
              // delete
              this._loadingService.show();
              this._stageService
                .deleteMatchesInStage(ids, this.stage.id)
                .toPromise()
                .then((resp) => {
                  this._toastr.success(
                    this._translateService.instant('Deleted successfully')
                  );
                  dt.ajax.reload();
                  this.onDataChange.emit(resp);
                });
            }
          });
        }
      },
      {
        text: `<i class="fas fa-file-export mr-1"></i> ${this._translateService.instant(
          'Export'
        )}`,
        extend: 'csv',
        action: async (e: any, dt: any, button: any, config: any) => {
          const data = dt.buttons.exportData();
          await this._exportService.exportCsv(data, 'Matches.csv');
        }
      },
      {
        text: `<i class="fa-solid fa-code"></i> ${this._translateService.instant(
          'Define Team'
        )}`,
        action: (e, dt, node, config) => {
          let selectedRows = dt.rows({ selected: true }).data();
          // check if selected rows > 1
          if (selectedRows.length > 1) {
            Swal.fire({
              title: this._translateService.instant('Notification'),
              text: this._translateService.instant(
                'Please select only one match'
              ),
              icon: 'warning',
              confirmButtonText: this._translateService.instant('Ok'),
              customClass: {
                confirmButton: 'btn btn-primary'
              }
            });
            return;
          }
          this.editor('edit', 'updateRankMatch');
        },
        extend: 'selected'
      },
      {
        text: this._translateService.instant('Columns'),
        extend: 'colvis'
      }
    ];

    // if stage type is league
    if (this.stage.type != AppConfig.TOURNAMENT_TYPES.league) {
      // remove first button
      btns.splice(0, 1);
    }

    if (
      this.stage.type != AppConfig.TOURNAMENT_TYPES.knockout ||
      this.tournament.type_knockout === AppConfig.KNOCKOUT_TYPES.type4
    ) {
      btns.splice(-2, 1);
    }

    if (
      this.tournament.type_knockout != AppConfig.KNOCKOUT_TYPES.type4 &&
      this.stage.type === AppConfig.TOURNAMENT_TYPES.knockout
    ) {
      btns.splice(0, 1);
      btns.splice(-4, 1);
    }

    this.dtOptions = {
      dom: this._commonsService.dataTableDefaults.dom,
      select: window.innerWidth > 768 ? 'os' : 'multi',
      // serverSide: true,
      rowId: 'id',
      rowGroup: { dataSrc: 'group_round' },
      order: [[2, 'asc']],
      ajax: (dataTablesParameters: any, callback) => {
        // add season id
        this._http
          .post<any>(
            `${environment.apiUrl}/stage-matches/all-in-stage/${this.stage.id}`,
            dataTablesParameters
          )
          .subscribe((resp: any) => {
            // find fields has key location_id and set options
            this.fields.forEach((field) => {
              if (field.key === 'location_id') {
                field.props.options = resp.options.location;
              }
              if (field.key === 'home_team_id') {
                field.props.options = resp.options.teams;
              }
              if (field.key === 'away_team_id') {
                field.props.options = resp.options.teams;
              }
            });

            this.rank_fields.forEach((field) => {
              if (field.key === 'home_label_id') {
                field.props.options = resp.options.rank_label;
              }
              if (field.key === 'away_label_id') {
                field.props.options = resp.options.rank_label;
              }
            });

            this.filterFriendlyMatches(this.friendlyMatchesActive);

            this.hasMatch = resp.data.length > 0;

            callback({
              recordsTotal: resp.recordsTotal,
              recordsFiltered: resp.recordsFiltered,
              data: resp.data
            });
          });
      },
      initComplete: () => {
        this.isComplete = true;
      },
      responsive: false,
      scrollX: true,
      language: this._commonsService.dataTableDefaults.lang,
      columnDefs: [
        { responsivePriority: 1, targets: -7 },
        { responsivePriority: 1, targets: -6 },
        { responsivePriority: 1, targets: -5 },
        { responsivePriority: 1, targets: -4 },
        { responsivePriority: 1, targets: -3 },
        { responsivePriority: 1, targets: -2 }
      ],
      columns: [
        { data: 'order', visible: false },
        { data: 'match_type', visible: false },
        { data: 'group_round', visible: false },
        {
          title: this._translateService.instant('No'),
          data: 'match_number',
          visible: this.stage.type !== AppConfig.TOURNAMENT_TYPES.league,
          render: function (data, type, row, meta) {
            return data ? `${data}` : `${meta.row + 1}`;
          }
        },
        {
          title: this._translateService.instant('Date'),
          data: 'date',
          orderable: false,
          className: 'text-center p-1',
          render: function (data, type, row) {
            let content = '';
            if (!data || data === 'TBD') {
              content = 'TBD';
            } else {
              // format to HH:mm from ISO 8601
              content = moment(data).format('YYYY-MM-DD');
            }
            return `<p style="margin:0; min-width: max-content">${content}</p>`;
          }
        },
        {
          title: this._translateService.instant('Start time'),
          data: 'start_time_short',
          orderable: false,
          className: 'text-center p-1',
          render: function (data, type, row) {
            if (
              !data ||
              row.start_time_short == 'TBD' ||
              !row.start_time_short
            ) {
              return 'TBD';
            }
            // format to HH:mm from ISO 8601
            return data;
          }
        },
        {
          title: this._translateService.instant('End time'),
          data: 'end_time_short',
          orderable: false,
          className: 'text-center p-1',
          render: function (data, type, row) {
            if (!data || row.end_time_short == 'TBD' || !row.end_time_short) {
              return 'TBD';
            }
            // format to HH:mm from ISO 8601
            return data;
          }
        },
        {
          title: this._translateService.instant('Location'),
          data: 'location_name'
        },
        {
          title: this._translateService.instant('Home team'),
          data: 'home_team_name',
          className: 'text-center p-1',
          orderable: false,
          render: function (data, type, row) {
            if (data === 'TBD') {
              if (row.home_text) {
                return row.home_text;
              } else if (row.home_label && row.home_label.label) {
                return row.home_label.label;
              }
            }
            return data;
          }
        },
        {
          data: null,
          className: 'text-center p-0 font-weight-bolder',
          render: function (data, type, row) {
            return `VS`;
          },
          orderable: false
        },
        {
          title: this._translateService.instant('Away team'),
          data: 'away_team_name',
          className: 'text-center p-1',
          orderable: false,
          render: function (data, type, row) {
            if (data === 'TBD') {
              if (row.away_text) {
                return row.away_text;
              } else if (row.away_label && row.away_label.label) {
                return row.away_label.label;
              }
            }
            return data;
          }
        },
        {
          title: this._translateService.instant('Home score'),
          data: 'home_score',
          className: 'text-center p-1',
          orderable: false,
          render: function (data, type, row) {
            if (row.home_penalty != null) {
              return `${data}<br> ( ${row.home_penalty} )`;
            }
            return data;
          }
        },
        {
          data: null,
          className: 'text-center p-0',
          orderable: false,
          render: function (data, type, row) {
            return ` - `;
          }
        },
        {
          title: this._translateService.instant('Away score'),
          data: 'away_score',
          className: 'text-center p-1',
          orderable: false,
          render: function (data, type, row) {
            if (row.away_penalty != null) {
              return `${data}<br> ( ${row.away_penalty} )`;
            }
            return data;
          }
        },
        {
          title: this._translateService.instant('Referees'),
          visible: false,
          data: 'referees',
          render: (data) => {
            return `<div class="d-flex flex-column gap-1">
              ${data.map((item) => {
              const refereeName = item.user ? `${item.user.first_name} ${item.user.last_name}` : item.referee_name;
              return `<p style="min-width: max-content; margin: 0">${refereeName}</p>`;
            }).join('')}
            </div>`;
          }
        },
        {
          title: this._translateService.instant('Status'),
          data: 'status',
          render: (data: string, type, row) => {
            const now = moment();
            const currentTime = now.format('HH:mm');
            const today = now.format('YYYY-MM-DD');
            if (!data) {
              // check if match time is over
              if (row.end_time_short == 'TBD') {
                return `<span class="badge badge-secondary text-capitalize">TBD</span>`;
              } else {
                if (
                  (row.date == today && row.end_time_short < currentTime) ||
                  row.date < today
                ) {
                  return `<span class="badge badge-success text-capitalize">${this._translateService.instant(
                    'Finished'
                  )}</span>`;
                } else if (
                  row.date == today &&
                  row.start_time_short <= currentTime &&
                  row.end_time_short >= currentTime
                ) {
                  return `<span class="badge badge-warning text-capitalize">${this._translateService.instant(
                    'In Progress'
                  )}</span>`;
                } else if (
                  row.date > today ||
                  (row.date == today && row.start_time_short > currentTime)
                ) {
                  return `<span class="badge badge-info text-capitalize">${this._translateService.instant(
                    'Upcoming'
                  )}</span>`;
                }
              }
            } else {
              if (AppConfig.CANCEL_MATCH_TYPES.includes(data)) {
                return `<span class="badge badge-danger text-capitalize">${data}</span>`;
              } else {
                return `<span class="badge badge-secondary text-capitalize">${data}</span>`;
              }
            }
          },
          className: 'text-center p-1',
          orderable: false
        }
      ],
      lengthMenu: [
        [25, 50, 100, -1],
        [25, 50, 100, 'All']
      ],
      buttons: {
        dom: this._commonsService.dataTableDefaults.buttons.dom,
        buttons: btns
      }
    };

    switch (this.stage.type) {
      case AppConfig.TOURNAMENT_TYPES.knockout:
        this.dtOptions.order = [[0, 'asc']];
        // add rowGroup
        this.dtOptions.rowGroup = { dataSrc: 'round_name' };
        // insert round_name column at index 6
        this.dtOptions.columns.splice(7, 0, {
          title: this._translateService.instant('Name'),
          data: 'round_name',
          className: 'text-center',
          render: function (data) {
            return `<p style="margin:0; min-width: max-content">${data}</p>`;
          }
        });

        break;
      case AppConfig.TOURNAMENT_TYPES.groups:
        this.dtOptions.order = [[2, 'asc']];

        // insert round_name column at index 5
        this.dtOptions.columns.splice(7, 0, {
          title: this._translateService.instant('Round'),
          data: 'round_name',
          className: 'text-center',
          render: function (data, type, row) {
            // split round_name by - and get the last item
            if (!data) return '';
            let round_name = data.split('-').pop();
            return `<p style="margin:0; min-width: max-content">${round_name}</p>`;
          }
        });
        break;
      case AppConfig.TOURNAMENT_TYPES.league:
        // clear rowGroup
        this.dtOptions.rowGroup = null;
        this.dtOptions.columns.splice(7, 0, {
          title: this._translateService.instant('Round'),
          data: 'round_name',
          className: 'text-center',
          render: function (data) {
            console.log('data', data);
            return `<p style="margin:0; min-width: max-content">${data ?? ''}</p>`;
          }
        });
        // order by round_level
        this.dtOptions.order = [[5, 'asc']];
        break;

      default:
        break;
    }
  }

  addOrRemoveRoundLevel(is_friendly: boolean) {
    const round_level = {
      key: 'round_level',
      type: 'select',
      props: {
        label: this._translateService.instant('Round level'),
        placeholder: this._translateService.instant('Select round level'),
        // required: true,
        options: this.roundLevelOpts
      }
    };

    if (is_friendly) {
      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {
        // remove round_level from editMatch
        const round_level_index = (this.editMatch as any).findIndex(
          (item) => item.key === 'round_level'
        );
        // check if round_level exist
        if (round_level_index > -1) {
          (this.editMatch as any).splice(round_level_index, 1);
        }
      }
    } else {
      if (this.stage.type === AppConfig.TOURNAMENT_TYPES.league) {
        const round_level_index = (this.editMatch as any).findIndex(
          (item) => item.key === 'round_level'
        );
        // check if round_level not exist
        if (round_level_index < 0) {
          (this.editMatch as any).splice(2, 0, {
            ...round_level
          });
        }
      }
    }
  }

  filterFriendlyMatches(active) {
    this.friendlyMatchesActive = active;
    this.addOrRemoveRoundLevel(active);
    // deselect all rows
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {
      // round level backup
      dtInstance.rows().deselect();
      if (!active) {
        // show column round name
        dtInstance.column(5).visible(true);
        dtInstance.column(1).search(1).draw();
        // disable button auto generate
        let btns = dtInstance.button('#auto-generate-btn');
        console.log(btns);
        if (btns) {
          btns.enable();
        }
      } else {
        // hide column round name
        dtInstance.column(5).visible(true);
        dtInstance.column(1).search(2).draw();
        // enable button auto generate
        let btns = dtInstance.button('#auto-generate-btn');
        console.log(btns);
        if (btns) {
          btns.disable();
        }
      }
    });
  }

  checkRowIsCancelled(row) {
    let hasCancel = false;
    // if row has status in AppConfig.CANCEL_MATCH_TYPES
    if (AppConfig.CANCEL_MATCH_TYPES.includes(row.status)) {
      hasCancel = true;
    }
    return hasCancel;
  }

  private _generateMatches() {
    this._stageService.autoGenerateMatches(this.stage.id).subscribe(
      (resp: any) => {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
          dtInstance.ajax.reload();
          this.onDataChange.emit(resp);
        });
      },
      (error) => {
        if (error.warning) {
          Swal.fire({
            title: this._translateService.instant('Cannot Auto Generate'),
            html: error.warning.replace(/\n/g, '<br>'),
            icon: 'warning',
            confirmButtonText: this._translateService.instant('OK'),
            customClass: {
              confirmButton: 'btn btn-primary'
            }
          });
          return;
        } else {
          Swal.fire({
            title: this._translateService.instant('Error'),
            text: error.message,
            icon: 'error',
            confirmButtonText: this._translateService.instant('OK'),
            customClass: {
              confirmButton: 'btn btn-primary'
            }
          });
        }
      }
    );
  }

  autoGenerateMatches() {
    console.log('auto generate matches', this.hasMatch);
    // check if matches not null
    if (this.hasMatch) {
      this._loadingService.show();

      Swal.fire({
        title: `${this._translateService.instant('Are you sure?')}`,
        html: `
                <p>${this._translateService.instant(
          'All current matches will be deleted unless they contain information'
        )}.</p>
                <div class="swal2-checkbox-container" style="display:flex;justify-content:center;gap: 10px;">
                    <input type="checkbox" id="confirmCheckbox" >
                    <label for="confirmCheckbox" class="swal2-label">${this._translateService.instant(
          'I confirm that I want to generate all current matches'
        )}</label>
                    <div class="text-danger swal2-label" id="swal2-validation-message" style="display: none;"></div>
                </div>
            `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: this._translateService.instant('Yes'),
        cancelButtonText: this._translateService.instant('No')
      }).then((result) => {
        if (result.isConfirmed) {
          this._generateMatches();
        } else {
          this._loadingService.dismiss();
        }
      });
    } else {
      if (this.isComplete) {
        this._generateMatches();
      }
    }
  }

  resetScore(stage_match_id) {
    console.log(`reset score for ${stage_match_id}`);
    this._loadingService.show();
    this._stageService.resetScore(stage_match_id).subscribe(
      (resp: any) => {
        console.log(resp);

        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
          dtInstance.ajax.reload();
          this.onDataChange.emit(resp);
        });
      },
      (error) => {
        Swal.fire({
          title: this._translateService.instant('Error'),
          text: error.message,
          icon: 'error',
          confirmButtonText: this._translateService.instant('OK'),
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        });
      }
    );
  }

  swapTeams(stage_match: StageMatch) {
    this._loadingService.show();
    this._stageService.swapTeams(stage_match).subscribe((resp: any) => {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.ajax.reload();
        this.onDataChange.emit(resp);
      });
    });
  }

  onSuccess($event) {
    if (this.is_score_updated) {
      this.onUpdateScore.emit($event);
    }
    this.onDataChange.emit($event);
  }

  checkValidateUpdateScore(row) {
    return !(!row.date || !row.start_time || !row.end_time || !row.location);
  }

  editor(action, fields, row?) {
    this.params.use_data = false;
    this.paramsToPost = {
      stage_type: this.stage.type,
      type_action: fields
    };
    // check has row with status is pass in selected rows
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      let selectedRows = dtInstance.rows({ selected: true }).data();

      switch (fields) {
        case 'editMatch':
          this.params.title.edit = this._translateService.instant('Edit match');

          this.fields = [...this.editMatch, {
            key: 'match_status',
            type: 'input',
            props: {
              type: 'hidden'
            },
            defaultValue: selectedRows[0]?.status
          }].map((item) => ({ ...item }));
          console.log('fields', this.fields);
          this.is_score_updated = false;

          this.fields.forEach((item) => {
            if (item.key === 'referee') {
              item.value = selectedRows[0]?.referees?.map((r) => r.id) || [];
            }
          });

          break;
        case 'updateScore':
          this.params.title.edit =
            this._translateService.instant('Update Score');
          this.fields = this.updateScore;
          this.is_score_updated = true;
          break;
        case 'cancelMatch':
          this.params.title.edit =
            this._translateService.instant('Cancel Match');
          this.fields = this.cancelMatch;
          this.is_score_updated = false;
          break;
        case 'addReplaceMatch':
          console.log(row);

          let paramsPost = {
            'data[0][round_name]': row.round_name,
            'data[0][round_level]': row.round_level,
            'data[0][match_id]': row.id,
            'data[0][match_number]': row.match_number || 0
          };
          // merge paramsToPost and paramsPost
          this.paramsToPost = { ...this.paramsToPost, ...paramsPost };
          this.params.title.edit =
            this._translateService.instant('Add Replace Match');
          this.fields = this.editMatch;
          this.params.use_data = true;
          break;
        case 'updateRankMatch':
          this.params.title.edit = this._translateService.instant('Edit Rank');
          this.fields = this.updateRankMatch;
          this.is_score_updated = false;
          break;
        default:
          this.params.title.edit = this._translateService.instant('Add Match');
          this.fields = this.editMatch.map((item) => {
            if (item.key === 'referee') {
              item['defaultValue'] = [];
              item['value'] = [];
            }
            return item;
          });
          this.is_score_updated = false;
          break;
      }
      this.fields_subject.next(this.fields);
      this.params.action = action;
      this.params.row = row ? row : null;

      this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();
    });
  }

  onCloseSidebar(event) {
    this.editMatch.forEach((e) => {
      if (e.key === 'referee') {
        e['defaultValue'] = [];
        e['value'] = [];
      }
    });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      // race condition fails unit tests if dtOptions isn't sent with dtTrigger
      this.dtTrigger.next(this.dtOptions);
    }, 500);
  }

  ngOnDestroy(): void {
    this.dtTrigger.unsubscribe();
  }

  @ViewChild('modalAssignReferee')
  modalAssignReferee!: TemplateRef<any>;

  public isMultipleAssign = true;
  public selectedIds = [];
  public assignRefereeForm = new FormGroup({});
  public assignRefereeModel = {};
  public assignRefereeFields = [
    {
      key: 'list_referees',
      type: 'ng-select',
      props: {
        multiple: true,
        hideOnMultiple: true,
        defaultValue: [],
        label: this._translateService.instant('Match Referee'),
        placeholder: this._translateService.instant('Select match referees'),
        options: []
      },
      hooks: {}
    }
  ];


  openModal() {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api | any) => {
      let selectedRows = dtInstance.rows({ selected: true }).data();

      this.selectedIds = [];
      selectedRows.map((row) => {
        return this.selectedIds.push(row.id);
      });

      if (selectedRows.length > 1) {
        this.isMultipleAssign = true;
        this.assignRefereeModel = {
          ...this.assignRefereeModel,
          list_referees: []
        };
      } else {
        this.isMultipleAssign = false;
        this.assignRefereeModel = {
          ...this.assignRefereeModel,
          list_referees: selectedRows[0]?.referees?.map((r) => r.id) || []
        };
      }

      this._modalService.open(this.modalAssignReferee, {
        centered: true,
        size: 'lg',
        beforeDismiss: () => {
          this.assignRefereeModel = {};
          this.assignRefereeFields.forEach((item) => {
            if (item.key === 'list_referees') {
              item['defaultValue'] = [];
            }
          });
          return true;
        }
      });
    });
  }

  getListReferee() {
    this._stageService.getListRefereesByStageId(this.stage.id).subscribe((response) => {
      this.listReferees = response['data'];
      this.assignRefereeFields.forEach((item) => {
        if (item.key === 'list_referees') {
          item.props.options = response['data'].map((referee) => {
            console.log(referee);
            return {
              label: referee.user
                ? `${referee.user.first_name} ${referee.user.last_name}`
                : referee.referee_name,
              value: referee.id
            };
          });
        }
      });
    }
    );
  }

  onSubmitAssignReferee(event) {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.ajax.reload();
    });
  }

  autoschedule() {
    this._stageService.autoSchedule(this.stage.id).subscribe((response) => {
      console.log(response);
    })
  }
}
