{"ast": null, "code": "import { environment } from '../../../../environments/environment';\nimport { DataTableDirective } from 'angular-datatables';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"../../../services/commons.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/location.service\";\nimport * as i8 from \"../../../components/editor-sidebar/editor-sidebar.component\";\nimport * as i9 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i10 from \"angular-datatables\";\nimport * as i11 from \"app/layout/components/content-header/content-header.component\";\nexport class LocationsComponent {\n  constructor(_http, _coreSidebarService, _translateService, _commonsService, _titleService, renderer, _modalService, _locationService) {\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._titleService = _titleService;\n    this.renderer = renderer;\n    this._modalService = _modalService;\n    this._locationService = _locationService;\n    this.dtElement = DataTableDirective;\n    this.paramsToPost = {};\n    this.table_name = 'locations-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Create new location',\n        edit: 'Edit location',\n        remove: 'Delete location'\n      },\n      url: environment.apiUrl + '/locations/editor',\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'name',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Name'),\n        placeholder: this._translateService.instant('Enter name of location'),\n        required: true,\n        minLength: 2\n      }\n    }, {\n      key: 'address',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Address'),\n        placeholder: this._translateService.instant('Enter address of location'),\n        required: true,\n        minLength: 2\n      }\n    }, {\n      key: 'latitude',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Latitude'),\n        placeholder: this._translateService.instant('Enter latitude of location')\n      }\n    }, {\n      key: 'longitude',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Longitude'),\n        placeholder: this._translateService.instant('Enter longtitude of location')\n      }\n    }, {\n      key: 'surface',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Surface'),\n        placeholder: this._translateService.instant('Enter surface of location')\n      }\n    }, {\n      key: 'parking',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Parking'),\n        placeholder: this._translateService.instant('Enter parking of location')\n      }\n    }];\n    this.dtOptions = {};\n    this._titleService.setTitle('Table Locations');\n  }\n  ngOnInit() {\n    console.log('LocationsComponent OnInit');\n    this.contentHeader = {\n      headerTitle: 'Locations',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          isLink: false\n        }, {\n          name: 'Locations',\n          isLink: false\n        }]\n      }\n    };\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/locations/all`, dataTablesParameters).subscribe(resp => {\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      columnDefs: [{\n        responsivePriority: 0,\n        targets: -1\n      }, {\n        responsivePriority: 1,\n        targets: -2\n      }],\n      displayLength: -1,\n      columns: [{\n        title: this._translateService.instant('Name'),\n        data: 'name',\n        className: 'font-weight-bolder p-1'\n      }, {\n        title: this._translateService.instant('Address'),\n        data: 'address',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Latitude'),\n        data: 'latitude',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Longitude'),\n        data: 'longitude',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Surface'),\n        data: 'surface',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Parking'),\n        data: 'parking',\n        className: 'p-1'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('Add'),\n          action: () => this.editor('create')\n        }, {\n          text: '<i class=\"feather icon-edit\"></i> ' + this._translateService.instant('Edit'),\n          action: () => this.editor('edit'),\n          extend: 'selected'\n        }, {\n          text: '<i class=\"feather icon-trash\"></i> ' + this._translateService.instant('Delete'),\n          action: () => this.editor('remove'),\n          extend: 'selected'\n        }]\n      }\n    };\n  }\n  editor(action) {\n    this.params.action = action;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  formChange($event) {\n    this.form = $event;\n  }\n  deleteLocation() {\n    // handle get selected row\n    this.dtElement.dtInstance.then(dtInstance => {\n      let selectedRows = dtInstance.rows({\n        selected: true\n      }).data();\n      let ids = [];\n      selectedRows.map(row => {\n        ids.push(row.id);\n      });\n      // confirm delete\n      Swal.fire({\n        title: this._translateService.instant('Are you sure?'),\n        text: this._translateService.instant('You will not be able to recover this!'),\n        reverseButtons: true,\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this._translateService.instant('Yes'),\n        cancelButtonText: this._translateService.instant('No'),\n        buttonsStyling: false,\n        customClass: {\n          confirmButton: 'btn btn-primary ml-1',\n          cancelButton: 'btn btn-outline-primary'\n        }\n      }).then(result => {\n        if (result.isConfirmed) {\n          this._locationService.deleteLocation(ids).subscribe(data => {\n            Swal.fire({\n              title: this._translateService.instant('Deleted!'),\n              text: this._translateService.instant('Deleted successfully'),\n              icon: 'success',\n              showCancelButton: false,\n              confirmButtonText: this._translateService.instant('OK')\n            });\n            dtInstance.ajax.reload();\n          }, error => {\n            Swal.fire({\n              title: this._translateService.instant('Error'),\n              text: this._translateService.instant(error.message),\n              icon: 'error',\n              showCancelButton: false,\n              confirmButtonText: this._translateService.instant('OK')\n            });\n          });\n        }\n      });\n    });\n  }\n  onSubmitted() {\n    switch ('create') {\n      case 'create':\n        // if form is not empty\n        if (this.form) {\n          // this.paramsToPost = {\n          //   \"data[0][photo]\": \"hehe\",\n          // };\n        }\n        break;\n    }\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    console.log('LocationsComponent destroy');\n    // this.unlistener();\n  }\n  static #_ = this.ɵfac = function LocationsComponent_Factory(t) {\n    return new (t || LocationsComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreSidebarService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.Title), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LocationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LocationsComponent,\n    selectors: [[\"app-locations\"]],\n    viewQuery: function LocationsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"onSubmit\", \"formChange\"]],\n    template: function LocationsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"div\", 4)(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6)(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵlistener(\"formChange\", function LocationsComponent_Template_app_editor_sidebar_formChange_7_listener($event) {\n          return ctx.formChange($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"paramsToPost\", ctx.paramsToPost)(\"onSubmit\", ctx.onSubmitted);\n      }\n    },\n    dependencies: [i8.EditorSidebarComponent, i9.CoreSidebarComponent, i10.DataTableDirective, i11.ContentHeaderComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,QAAQ,sCAAsC;AAUlE,SAASC,kBAAkB,QAAQ,oBAAoB;AAOvD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;AAS9B,OAAM,MAAOC,kBAAkB;EAwF7BC,YACUC,KAAiB,EAClBC,mBAAuC,EACvCC,iBAAmC,EACnCC,eAA+B,EAC/BC,aAAoB,EACpBC,QAAmB,EACnBC,aAAuB,EACvBC,gBAAiC;IAPhC,UAAK,GAALP,KAAK;IACN,wBAAmB,GAAnBC,mBAAmB;IACnB,sBAAiB,GAAjBC,iBAAiB;IACjB,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,aAAQ,GAARC,QAAQ;IACR,kBAAa,GAAbC,aAAa;IACb,qBAAgB,GAAhBC,gBAAgB;IA9FzB,cAAS,GAAQX,kBAAkB;IAI5B,iBAAY,GAAQ,EAAE;IAEtB,eAAU,GAAG,iBAAiB;IAC9B,WAAM,GAAwB;MACnCY,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,eAAe;QACrBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAEnB,WAAW,CAACoB,MAAM,GAAG,mBAAmB;MAC7CC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,WAAM,GAAU,CACrB;MACEC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CAAC,wBAAwB,CAAC;QACrEE,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;;KAEd,EACD;MACEP,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChDC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CACzC,2BAA2B,CAC5B;QACDE,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;;KAEd,EACD;MACEP,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,UAAU,CAAC;QACjDC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CACzC,4BAA4B;;KAGjC,EACD;MACEJ,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,WAAW,CAAC;QAClDC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CACzC,8BAA8B;;KAGnC,EACD;MACEJ,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChDC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CACzC,2BAA2B;;KAGhC,EACD;MACEJ,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChDC,WAAW,EAAE,IAAI,CAACrB,iBAAiB,CAACoB,OAAO,CACzC,2BAA2B;;KAGhC,CACF;IAED,cAAS,GAAQ,EAAE;IAYjB,IAAI,CAAClB,aAAa,CAACsB,QAAQ,CAAC,iBAAiB,CAAC;EAChD;EAEAC,QAAQ;IACNC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,WAAW;MACxBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVd,IAAI,EAAE,EAAE;QACRe,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,UAAU;UAChBC,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,WAAW;UACjBC,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACC,SAAS,GAAG;MACfC,GAAG,EAAE,IAAI,CAACnC,eAAe,CAACoC,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAAC5C,KAAK,CACP6C,IAAI,CACH,GAAGlD,WAAW,CAACoB,MAAM,gBAAgB,EACrC4B,oBAAoB,CACrB,CACAG,SAAS,CAAEC,IAAS,IAAI;UACvBH,QAAQ,CAAC;YACPI,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAClD,eAAe,CAACoC,iBAAiB,CAACe,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACpD,eAAe,CAACoC,iBAAiB,CAACgB,UAAU;MAC7DC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CACvC;MACDC,aAAa,EAAE,CAAC,CAAC;MACjBC,OAAO,EAAE,CACP;QACElD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,MAAM,CAAC;QAC7C4B,IAAI,EAAE,MAAM;QACZW,SAAS,EAAE;OACZ,EACD;QACEnD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChD4B,IAAI,EAAE,SAAS;QACfW,SAAS,EAAE;OACZ,EACD;QACEnD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,UAAU,CAAC;QACjD4B,IAAI,EAAE,UAAU;QAChBW,SAAS,EAAE;OACZ,EACD;QACEnD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,WAAW,CAAC;QAClD4B,IAAI,EAAE,WAAW;QACjBW,SAAS,EAAE;OACZ,EACD;QACEnD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChD4B,IAAI,EAAE,SAAS;QACfW,SAAS,EAAE;OACZ,EACD;QACEnD,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,SAAS,CAAC;QAChD4B,IAAI,EAAE,SAAS;QACfW,SAAS,EAAE;OACZ,CACF;MACDC,OAAO,EAAE;QACPxB,GAAG,EAAE,IAAI,CAACnC,eAAe,CAACoC,iBAAiB,CAACuB,OAAO,CAACxB,GAAG;QACvDwB,OAAO,EAAE,CACP;UACEC,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAAC,KAAK,CAAC;UACvCL,MAAM,EAAE,MAAM,IAAI,CAAC+C,MAAM,CAAC,QAAQ;SACnC,EACD;UACED,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAAC,MAAM,CAAC;UACxCL,MAAM,EAAE,MAAM,IAAI,CAAC+C,MAAM,CAAC,MAAM,CAAC;UACjCC,MAAM,EAAE;SACT,EACD;UACEF,IAAI,EACF,qCAAqC,GACrC,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAAC,QAAQ,CAAC;UAC1CL,MAAM,EAAE,MAAM,IAAI,CAAC+C,MAAM,CAAC,QAAQ,CAAC;UACnCC,MAAM,EAAE;SACT;;KAGN;EACH;EAEAD,MAAM,CAAC/C,MAAM;IACX,IAAI,CAACiD,MAAM,CAACjD,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAAChB,mBAAmB,CAACkE,kBAAkB,CAAC,IAAI,CAAC1D,UAAU,CAAC,CAAC2D,UAAU,EAAE;EAC3E;EAEAC,UAAU,CAACC,MAAM;IACf,IAAI,CAACC,IAAI,GAAGD,MAAM;EACpB;EAEAE,cAAc;IACZ;IACA,IAAI,CAACC,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5D,IAAIE,YAAY,GAAGF,UAAU,CAACG,IAAI,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC,CAAC5B,IAAI,EAAE;MAC7D,IAAI6B,GAAG,GAAG,EAAE;MACZH,YAAY,CAACI,GAAG,CAAEC,GAAG,IAAI;QACvBF,GAAG,CAACG,IAAI,CAACD,GAAG,CAACE,EAAE,CAAC;MAClB,CAAC,CAAC;MAEF;MACAtF,IAAI,CAACuF,IAAI,CAAC;QACR1E,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,eAAe,CAAC;QACtDyC,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAClC,uCAAuC,CACxC;QACD+D,cAAc,EAAE,IAAI;QACpBC,IAAI,EAAE,SAAS;QACfC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI,CAACtF,iBAAiB,CAACoB,OAAO,CAAC,KAAK,CAAC;QACxDmE,gBAAgB,EAAE,IAAI,CAACvF,iBAAiB,CAACoB,OAAO,CAAC,IAAI,CAAC;QACtDoE,cAAc,EAAE,KAAK;QACrBC,WAAW,EAAE;UACXC,aAAa,EAAE,sBAAsB;UACrCC,YAAY,EAAE;;OAEjB,CAAC,CAAClB,IAAI,CAAEmB,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;UACtB,IAAI,CAACxF,gBAAgB,CAACiE,cAAc,CAACO,GAAG,CAAC,CAACjC,SAAS,CAAEI,IAAI,IAAI;YAC3DrD,IAAI,CAACuF,IAAI,CAAC;cACR1E,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,UAAU,CAAC;cACjDyC,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAAC,sBAAsB,CAAC;cAC5DgE,IAAI,EAAE,SAAS;cACfC,gBAAgB,EAAE,KAAK;cACvBC,iBAAiB,EAAE,IAAI,CAACtF,iBAAiB,CAACoB,OAAO,CAAC,IAAI;aACvD,CAAC;YACFoD,UAAU,CAAChC,IAAI,CAACsD,MAAM,EAAE;UAC1B,CAAC,EAAGC,KAAK,IAAI;YACXpG,IAAI,CAACuF,IAAI,CAAC;cACR1E,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACoB,OAAO,CAAC,OAAO,CAAC;cAC9CyC,IAAI,EAAE,IAAI,CAAC7D,iBAAiB,CAACoB,OAAO,CAAC2E,KAAK,CAACC,OAAO,CAAC;cACnDZ,IAAI,EAAE,OAAO;cACbC,gBAAgB,EAAE,KAAK;cACvBC,iBAAiB,EAAE,IAAI,CAACtF,iBAAiB,CAACoB,OAAO,CAAC,IAAI;aACvD,CAAC;UACJ,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;EAGJ;EAEA6E,WAAW;IACT,QAAQ,QAAQ;MACd,KAAK,QAAQ;QACX;QACA,IAAI,IAAI,CAAC5B,IAAI,EAAE;UACb;UACA;UACA;QAAA;QAEF;IAAM;EAEZ;EAEA6B,eAAe,IAAW;EAC1BC,WAAW;IACTzE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC;EACF;EAAC;qBA/RU/B,kBAAkB;EAAA;EAAA;UAAlBA,kBAAkB;IAAAwG;IAAAC;MAAA;uBAClB3G,kBAAkB;;;;;;;;;;;;QC3B/B4G,8BAA+C;QAGvCA,wCAAyE;QACzEA,8BAAkB;QACdA,yBAEM;QAEVA,iBAAM;QAKdA,uCAAqH;QACrCA;UAAA,OAAcC,sBAAkB;QAAA,EAAC;QAE7GD,iBAAqB;;;QAdGA,eAA+B;QAA/BA,iDAA+B;QAK9BA,eAAuB;QAAvBA,yCAAuB;QAMeA,eAAmB;QAAnBA,qCAAmB;QAC9DA,eAAmB;QAAnBA,qCAAmB", "names": ["environment", "DataTableDirective", "<PERSON><PERSON>", "LocationsComponent", "constructor", "_http", "_coreSidebarService", "_translateService", "_commonsService", "_titleService", "renderer", "_modalService", "_locationService", "editor_id", "table_name", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "key", "type", "props", "label", "instant", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "setTitle", "ngOnInit", "console", "log", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "dtOptions", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "lengthMenu", "columnDefs", "responsivePriority", "targets", "displayLength", "columns", "className", "buttons", "text", "editor", "extend", "params", "getSidebarRegistry", "toggle<PERSON><PERSON>", "formChange", "$event", "form", "deleteLocation", "dtElement", "dtInstance", "then", "selectedRows", "rows", "selected", "ids", "map", "row", "push", "id", "fire", "reverseButtons", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "result", "isConfirmed", "reload", "error", "message", "onSubmitted", "ngAfterViewInit", "ngOnDestroy", "selectors", "viewQuery", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\tables\\locations\\locations.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\tables\\locations\\locations.component.html"], "sourcesContent": ["import { environment } from '../../../../environments/environment';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from '../../../services/commons.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { Title } from '@angular/platform-browser';\r\nimport Swal from 'sweetalert2';\r\nimport { LocationService } from 'app/services/location.service';\r\n\r\n@Component({\r\n  selector: 'app-locations',\r\n  templateUrl: './locations.component.html',\r\n  styleUrls: ['./locations.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class LocationsComponent implements OnInit {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  // private unlistener: () => void;\r\n  // public\r\n  public form: FormGroup;\r\n  public paramsToPost: any = {};\r\n  public contentHeader: object;\r\n  public table_name = 'locations-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: 'Create new location',\r\n      edit: 'Edit location',\r\n      remove: 'Delete location',\r\n    },\r\n    url: environment.apiUrl + '/locations/editor',\r\n    method: 'POST',\r\n    action: 'create',\r\n  };\r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'name',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Name'),\r\n        placeholder: this._translateService.instant('Enter name of location'),\r\n        required: true,\r\n        minLength: 2,\r\n      },\r\n    },\r\n    {\r\n      key: 'address',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Address'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter address of location'\r\n        ),\r\n        required: true,\r\n        minLength: 2,\r\n      },\r\n    },\r\n    {\r\n      key: 'latitude',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Latitude'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter latitude of location'\r\n        ),\r\n      },\r\n    },\r\n    {\r\n      key: 'longitude',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Longitude'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter longtitude of location'\r\n        ),\r\n      },\r\n    },\r\n    {\r\n      key: 'surface',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Surface'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter surface of location'\r\n        ),\r\n      },\r\n    },\r\n    {\r\n      key: 'parking',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Parking'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter parking of location'\r\n        ),\r\n      },\r\n    },\r\n  ];\r\n\r\n  dtOptions: any = {};\r\n\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _titleService: Title,\r\n    public renderer: Renderer2,\r\n    public _modalService: NgbModal,\r\n    public _locationService: LocationService\r\n  ) {\r\n    this._titleService.setTitle('Table Locations');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('LocationsComponent OnInit');\r\n    this.contentHeader = {\r\n      headerTitle: 'Locations',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Settings',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: 'Locations',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/locations/all`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      columnDefs: [\r\n        { responsivePriority: 0, targets: -1 },\r\n        { responsivePriority: 1, targets: -2 },\r\n      ],\r\n      displayLength: -1,\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Address'),\r\n          data: 'address',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Latitude'),\r\n          data: 'latitude',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Longitude'),\r\n          data: 'longitude',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Surface'),\r\n          data: 'surface',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Parking'),\r\n          data: 'parking',\r\n          className: 'p-1',\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-plus\"></i> ' +\r\n              this._translateService.instant('Add'),\r\n            action: () => this.editor('create'),\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-edit\"></i> ' +\r\n              this._translateService.instant('Edit'),\r\n            action: () => this.editor('edit'),\r\n            extend: 'selected',\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-trash\"></i> ' +\r\n              this._translateService.instant('Delete'),\r\n            action: () => this.editor('remove'),\r\n            extend: 'selected',\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  editor(action) {\r\n    this.params.action = action;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  formChange($event) {\r\n    this.form = $event;\r\n  }\r\n\r\n  deleteLocation() {\r\n    // handle get selected row\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      let selectedRows = dtInstance.rows({ selected: true }).data();\r\n      let ids = [];\r\n      selectedRows.map((row) => {\r\n        ids.push(row.id);\r\n      });\r\n\r\n      // confirm delete\r\n      Swal.fire({\r\n        title: this._translateService.instant('Are you sure?'),\r\n        text: this._translateService.instant(\r\n          'You will not be able to recover this!'\r\n        ),\r\n        reverseButtons: true,\r\n        icon: 'warning',\r\n        showCancelButton: true,\r\n        confirmButtonText: this._translateService.instant('Yes'),\r\n        cancelButtonText: this._translateService.instant('No'),\r\n        buttonsStyling: false,\r\n        customClass: {\r\n          confirmButton: 'btn btn-primary ml-1',\r\n          cancelButton: 'btn btn-outline-primary'\r\n        }\r\n      }).then((result) => {\r\n        if (result.isConfirmed) {\r\n          this._locationService.deleteLocation(ids).subscribe((data) => {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Deleted!'),\r\n              text: this._translateService.instant('Deleted successfully'),\r\n              icon: 'success',\r\n              showCancelButton: false,\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            })\r\n            dtInstance.ajax.reload();\r\n          }, (error) => {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Error'),\r\n              text: this._translateService.instant(error.message),\r\n              icon: 'error',\r\n              showCancelButton: false,\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            })\r\n          });\r\n        }\r\n      });\r\n    });\r\n\r\n\r\n  }\r\n\r\n  onSubmitted() {\r\n    switch ('create') {\r\n      case 'create':\r\n        // if form is not empty\r\n        if (this.form) {\r\n          // this.paramsToPost = {\r\n          //   \"data[0][photo]\": \"hehe\",\r\n          // };\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void { }\r\n  ngOnDestroy(): void {\r\n    console.log('LocationsComponent destroy');\r\n    // this.unlistener();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <!-- content-header component -->\r\n        <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <!-- <h4 class=\"card-title\">Table Basic</h4> -->\r\n            </div>\r\n            <table datatable [dtOptions]=\"dtOptions\" class=\"table border row-border hover\"></table>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<!-- Modal -->\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n    <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\" (formChange)=\"formChange($event)\"\r\n        [paramsToPost]=\"paramsToPost\" [onSubmit]=\"onSubmitted\">\r\n    </app-editor-sidebar>\r\n</core-sidebar>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}