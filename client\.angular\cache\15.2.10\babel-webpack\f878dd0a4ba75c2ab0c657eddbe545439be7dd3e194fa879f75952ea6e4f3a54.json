{"ast": null, "code": "import { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\nimport { CommonModule } from '@angular/common';\nimport { EditorSidebarComponent } from './editor-sidebar.component';\nimport { CoreSidebarModule } from '@core/components';\nimport { FORMLY_CONFIG, FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { DatatimepickerComponent } from 'app/layout/components/datatimepicker/datatimepicker.component';\nimport { NgbAlertModule, NgbDatepickerModule, NgbNavModule, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\nimport { FormlyFieldFile } from '../file-type/file-type.component';\nimport { FileValueAccessor } from '../file-type/file-value-accessor';\nimport { NumberTypeComponent } from '../number-type/number-type.component';\nimport { ImageCropperTypeComponent } from '../image-cropper-type/image-cropper-type.component';\nimport { CropperWithDialogModule } from '../cropper-dialog/cropper-with-dialog.module';\nimport { NgSelectTypeComponent } from '../ng-select-type/ng-select-type.component';\nimport { FormlySelectModule } from '@ngx-formly/core/select';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { registerTranslateExtension } from 'app/translate.extension';\nimport { DetailsWrapperComponent } from 'app/pages/league-tournament/stages/stage-details/details-wraper.component';\nimport { RepeateFormTypeComponent } from '../repeate-form-type/repeate-form-type.component';\nimport { FormlyHorizontalWrapper } from './horizontal-wrapper';\nimport { FormlyFieldTabsVertical } from '../tabs-vertical-type/tabs-vertical-type.component';\nimport { FormlyFieldTabs } from '../tabs-type/tabs-type.component';\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\nimport { Ckeditor5TypeComponent } from '../ckeditor5-type/ckeditor5-type.component';\nimport { CustomDateParserFormatter } from '@core/services/custom-date-parser-formatter.service';\nimport { CustomDateInputComponent } from '../custom-date-input/custom-date-input.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@ngx-formly/core\";\nexport function serverValidationMessage(error, field) {\n  // console.log(error, field);\n  return error;\n}\nexport class EditorSidebarModule {\n  constructor(_translateService, config) {\n    config.addValidatorMessage('max', (error, field) => {\n      return _translateService.instant('Value must be less than') + ' ' + (field.props.max + 1);\n    });\n    config.addValidatorMessage('min', (error, field) => {\n      return _translateService.instant('Value must be greater than') + ' ' + (field.props.min - 1);\n    });\n    config.addValidatorMessage('minlength', (error, field) => {\n      let fieldName = _translateService.instant(field.key);\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      return _translateService.instant('This field must be at least', {\n        field: fieldName\n      }) + ' ' + field.props.minLength + ' ' + _translateService.instant('characters');\n    });\n    config.addValidatorMessage('maxlength', (error, field) => {\n      let fieldName = _translateService.instant(field.key);\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      return _translateService.instant('This field must be less than', {\n        field: fieldName\n      }) + ' ' + field.props.maxLength + ' ' + _translateService.instant('characters');\n    });\n    config.addValidatorMessage('pattern', (error, field) => {\n      let fieldName = _translateService.instant(field.key);\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      return _translateService.instant('This field is invalid', {\n        field: fieldName\n      });\n    });\n    config.addValidatorMessage('required', (error, field) => {\n      let fieldName = _translateService.instant(field.key);\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      // replace '_' with ' '\n      fieldName = fieldName.replace(/_/g, ' ');\n      return _translateService.instant('This field is required', {\n        field: fieldName\n      });\n    });\n  }\n  static #_ = this.ɵfac = function EditorSidebarModule_Factory(t) {\n    return new (t || EditorSidebarModule)(i0.ɵɵinject(i1.TranslateService), i0.ɵɵinject(i2.FormlyConfig));\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: EditorSidebarModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: FORMLY_CONFIG,\n      multi: true,\n      useFactory: registerTranslateExtension,\n      deps: [TranslateService]\n    }, {\n      provide: NgbDateParserFormatter,\n      useClass: CustomDateParserFormatter\n    }],\n    imports: [FormlySelectModule, NgSelectModule, CommonModule, FormsModule, TranslateModule, CoreSidebarModule, ReactiveFormsModule, CropperWithDialogModule, NgbAlertModule, NgbNavModule, CKEditorModule, FormlyModule.forRoot({\n      types: [{\n        name: 'datetime',\n        component: DatatimepickerComponent\n      }, {\n        name: 'file',\n        component: FormlyFieldFile,\n        wrappers: ['form-field']\n      }, {\n        name: 'tabs-vertical',\n        component: FormlyFieldTabsVertical,\n        wrappers: ['form-field']\n      }, {\n        name: 'ckeditor5',\n        component: Ckeditor5TypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'tabs',\n        component: FormlyFieldTabs,\n        wrappers: ['form-field']\n      }, {\n        name: 'core-touchspin',\n        component: NumberTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'image-cropper',\n        component: ImageCropperTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'ng-select',\n        component: NgSelectTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'details',\n        component: DetailsWrapperComponent\n      }, {\n        name: 'repeat',\n        component: RepeateFormTypeComponent\n      }],\n      wrappers: [{\n        name: 'form-field-horizontal',\n        component: FormlyHorizontalWrapper\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    }), FormlyBootstrapModule, NgbDatepickerModule, FormlyModule, FormlyBootstrapModule, ReactiveFormsModule, CropperWithDialogModule, NgbAlertModule, CKEditorModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EditorSidebarModule, {\n    declarations: [EditorSidebarComponent, DatatimepickerComponent, FormlyFieldFile, FileValueAccessor, ImageCropperTypeComponent, NgSelectTypeComponent, FormlyHorizontalWrapper, FormlyFieldTabsVertical, FormlyFieldTabs, CustomDateInputComponent],\n    imports: [FormlySelectModule, NgSelectModule, CommonModule, FormsModule, TranslateModule, CoreSidebarModule, ReactiveFormsModule, CropperWithDialogModule, NgbAlertModule, NgbNavModule, CKEditorModule, i2.FormlyModule, FormlyBootstrapModule, NgbDatepickerModule],\n    exports: [EditorSidebarComponent, FormlyModule, FormlyBootstrapModule, ReactiveFormsModule, CropperWithDialogModule, NgbAlertModule, CKEditorModule]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,qBAAqB;AAEvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,iBAAiB,QAAQ,kBAAkB;AAEpD,SACEC,aAAa,EAGbC,YAAY,QACP,kBAAkB;AACzB,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,uBAAuB,QAAQ,+DAA+D;AAEvG,SACEC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,sBAAsB,QACjB,4BAA4B;AACnC,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,yBAAyB,QAAQ,oDAAoD;AAC9F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,0BAA0B,QAAQ,yBAAyB;AACpE,SAASC,uBAAuB,QAAQ,2EAA2E;AACnH,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,uBAAuB,QAAQ,oDAAoD;AAC5F,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,yBAAyB,QAAQ,qDAAqD;AAC/F,SAASC,wBAAwB,QAAQ,kDAAkD;;;;AAC3F,OAAM,SAAUC,uBAAuB,CAACC,KAAU,EAAEC,KAAwB;EAC1E;EACA,OAAOD,KAAK;AACd;AA+FA,OAAM,MAAOE,mBAAmB;EAC9BC,YAAYC,iBAAmC,EAAEC,MAAoB;IACnEA,MAAM,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACjD,OACEG,iBAAiB,CAACG,OAAO,CAAC,yBAAyB,CAAC,GACpD,GAAG,IACFN,KAAK,CAACO,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC;IAEzB,CAAC,CAAC;IACFJ,MAAM,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACjD,OACEG,iBAAiB,CAACG,OAAO,CAAC,4BAA4B,CAAC,GACvD,GAAG,IACFN,KAAK,CAACO,KAAK,CAACE,GAAG,GAAG,CAAC,CAAC;IAEzB,CAAC,CAAC;IACFL,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACvD,IAAIU,SAAS,GAAGP,iBAAiB,CAACG,OAAO,CAACN,KAAK,CAACW,GAAa,CAAC;MAC9DD,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC;MAClE,OACEX,iBAAiB,CAACG,OAAO,CAAC,6BAA6B,EAAE;QACvDN,KAAK,EAAEU;OACR,CAAC,GACF,GAAG,GACHV,KAAK,CAACO,KAAK,CAACQ,SAAS,GACrB,GAAG,GACHZ,iBAAiB,CAACG,OAAO,CAAC,YAAY,CAAC;IAE3C,CAAC,CAAC;IACFF,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACvD,IAAIU,SAAS,GAAGP,iBAAiB,CAACG,OAAO,CAACN,KAAK,CAACW,GAAa,CAAC;MAC9DD,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC;MAClE,OACEX,iBAAiB,CAACG,OAAO,CAAC,8BAA8B,EAAE;QACxDN,KAAK,EAAEU;OACR,CAAC,GACF,GAAG,GACHV,KAAK,CAACO,KAAK,CAACS,SAAS,GACrB,GAAG,GACHb,iBAAiB,CAACG,OAAO,CAAC,YAAY,CAAC;IAE3C,CAAC,CAAC;IACFF,MAAM,CAACC,mBAAmB,CAAC,SAAS,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACrD,IAAIU,SAAS,GAAGP,iBAAiB,CAACG,OAAO,CAACN,KAAK,CAACW,GAAa,CAAC;MAC9DD,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC;MAClE,OAAOX,iBAAiB,CAACG,OAAO,CAAC,uBAAuB,EAAE;QACxDN,KAAK,EAAEU;OACR,CAAC;IACJ,CAAC,CAAC;IACFN,MAAM,CAACC,mBAAmB,CAAC,UAAU,EAAE,CAACN,KAAK,EAAEC,KAAK,KAAI;MACtD,IAAIU,SAAS,GAAGP,iBAAiB,CAACG,OAAO,CAACN,KAAK,CAACW,GAAa,CAAC;MAC9DD,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC;MAClE;MACAJ,SAAS,GAAGA,SAAS,CAACO,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACxC,OAAOd,iBAAiB,CAACG,OAAO,CAAC,wBAAwB,EAAE;QACzDN,KAAK,EAAEU;OACR,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC;qBA1DUT,mBAAmB;EAAA;EAAA;UAAnBA;EAAmB;EAAA;eAtBnB,CACT;MACEiB,OAAO,EAAE9C,aAAa;MACtB+C,KAAK,EAAE,IAAI;MACXC,UAAU,EAAEhC,0BAA0B;MACtCiC,IAAI,EAAE,CAACrD,gBAAgB;KACxB,EACD;MACEkD,OAAO,EAAEvC,sBAAsB;MAC/B2C,QAAQ,EAAE1B;KACX,CACF;IAAA2B,UArECrC,kBAAkB,EAClBC,cAAc,EACdlB,YAAY,EACZJ,WAAW,EACXE,eAAe,EACfI,iBAAiB,EACjBL,mBAAmB,EACnBkB,uBAAuB,EACvBR,cAAc,EACdE,YAAY,EACZgB,cAAc,EACdrB,YAAY,CAACmD,OAAO,CAAC;MACnBC,KAAK,EAAE,CACL;QAAEC,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEpD;MAAuB,CAAE,EACxD;QAAEmD,IAAI,EAAE,MAAM;QAAEC,SAAS,EAAE/C,eAAe;QAAEgD,QAAQ,EAAE,CAAC,YAAY;MAAC,CAAE,EACtE;QACEF,IAAI,EAAE,eAAe;QACrBC,SAAS,EAAEnC,uBAAuB;QAClCoC,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEF,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAEhC,sBAAsB;QACjCiC,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEF,IAAI,EAAE,MAAM;QACZC,SAAS,EAAElC,eAAe;QAC1BmC,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEF,IAAI,EAAE,gBAAgB;QACtBC,SAAS,EAAE7C,mBAAmB;QAC9B8C,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEF,IAAI,EAAE,eAAe;QACrBC,SAAS,EAAE5C,yBAAyB;QACpC6C,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEF,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE1C,qBAAqB;QAChC2C,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QAAEF,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAEtC;MAAuB,CAAE,EACvD;QAAEqC,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAErC;MAAwB,CAAE,CACxD;MACDsC,QAAQ,EAAE,CACR;QAAEF,IAAI,EAAE,uBAAuB;QAAEC,SAAS,EAAEpC;MAAuB,CAAE,CACtE;MACDsC,kBAAkB,EAAE,CAClB;QAAEH,IAAI,EAAE,aAAa;QAAEI,OAAO,EAAEhC;MAAuB,CAAE;KAE5D,CAAC,EACFxB,qBAAqB,EACrBG,mBAAmB,EAgBnBJ,YAAY,EACZC,qBAAqB,EACrBR,mBAAmB,EACnBkB,uBAAuB,EACvBR,cAAc,EACdkB,cAAc;EAAA;;;2EAGLO,mBAAmB;IAAA8B,eA5F5B7D,sBAAsB,EACtBK,uBAAuB,EACvBK,eAAe,EACfC,iBAAiB,EACjBE,yBAAyB,EACzBE,qBAAqB,EACrBM,uBAAuB,EACvBC,uBAAuB,EACvBC,eAAe,EACfI,wBAAwB;IAAA0B,UAGxBrC,kBAAkB,EAClBC,cAAc,EACdlB,YAAY,EACZJ,WAAW,EACXE,eAAe,EACfI,iBAAiB,EACjBL,mBAAmB,EACnBkB,uBAAuB,EACvBR,cAAc,EACdE,YAAY,EACZgB,cAAc,mBA6CdpB,qBAAqB,EACrBG,mBAAmB;IAAAuD,UAenB9D,sBAAsB,EACtBG,YAAY,EACZC,qBAAqB,EACrBR,mBAAmB,EACnBkB,uBAAuB,EACvBR,cAAc,EACdkB,cAAc;EAAA;AAAA", "names": ["FormsModule", "ReactiveFormsModule", "TranslateModule", "TranslateService", "CommonModule", "EditorSidebarComponent", "CoreSidebarModule", "FORMLY_CONFIG", "FormlyModule", "FormlyBootstrapModule", "DatatimepickerComponent", "NgbAlertModule", "NgbDatepickerModule", "NgbNavModule", "NgbDateParserFormatter", "FormlyFieldFile", "FileValueAccessor", "NumberTypeComponent", "ImageCropperTypeComponent", "CropperWithDialogModule", "NgSelectTypeComponent", "FormlySelectModule", "NgSelectModule", "registerTranslateExtension", "DetailsWrapperComponent", "RepeateFormTypeComponent", "FormlyHorizontalWrapper", "FormlyFieldTabsVertical", "FormlyFieldTabs", "CKEditorModule", "Ckeditor5TypeComponent", "CustomDateParserFormatter", "CustomDateInputComponent", "serverValidationMessage", "error", "field", "EditorSidebarModule", "constructor", "_translateService", "config", "addValidatorMessage", "instant", "props", "max", "min", "fieldName", "key", "char<PERSON>t", "toUpperCase", "slice", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "replace", "provide", "multi", "useFactory", "deps", "useClass", "imports", "forRoot", "types", "name", "component", "wrappers", "validationMessages", "message", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\components\\editor-sidebar\\editor-sidebar.module.ts"], "sourcesContent": ["import { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { EditorSidebarComponent } from './editor-sidebar.component';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport {\r\n  FORMLY_CONFIG,\r\n  FormlyConfig,\r\n  FormlyFieldConfig,\r\n  FormlyModule,\r\n} from '@ngx-formly/core';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { DatatimepickerComponent } from 'app/layout/components/datatimepicker/datatimepicker.component';\r\nimport { Ng2FlatpickrModule } from 'ng2-flatpickr';\r\nimport {\r\n  NgbAlertModule,\r\n  NgbDatepickerModule,\r\n  NgbNavModule,\r\n  NgbDateParserFormatter,\r\n} from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldFile } from '../file-type/file-type.component';\r\nimport { FileValueAccessor } from '../file-type/file-value-accessor';\r\nimport { NumberTypeComponent } from '../number-type/number-type.component';\r\nimport { ImageCropperTypeComponent } from '../image-cropper-type/image-cropper-type.component';\r\nimport { CropperWithDialogModule } from '../cropper-dialog/cropper-with-dialog.module';\r\nimport { NgSelectTypeComponent } from '../ng-select-type/ng-select-type.component';\r\nimport { FormlySelectModule } from '@ngx-formly/core/select';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { registerTranslateExtension } from 'app/translate.extension';\r\nimport { DetailsWrapperComponent } from 'app/pages/league-tournament/stages/stage-details/details-wraper.component';\r\nimport { RepeateFormTypeComponent } from '../repeate-form-type/repeate-form-type.component';\r\nimport { FormlyHorizontalWrapper } from './horizontal-wrapper';\r\nimport { FormlyFieldTabsVertical } from '../tabs-vertical-type/tabs-vertical-type.component';\r\nimport { FormlyFieldTabs } from '../tabs-type/tabs-type.component';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { Ckeditor5TypeComponent } from '../ckeditor5-type/ckeditor5-type.component';\r\nimport { CustomDateParserFormatter } from '@core/services/custom-date-parser-formatter.service';\r\nimport { CustomDateInputComponent } from '../custom-date-input/custom-date-input.component';\r\nexport function serverValidationMessage(error: any, field: FormlyFieldConfig) {\r\n  // console.log(error, field);\r\n  return error;\r\n}\r\n@NgModule({\r\n  declarations: [\r\n    EditorSidebarComponent,\r\n    DatatimepickerComponent,\r\n    FormlyFieldFile,\r\n    FileValueAccessor,\r\n    ImageCropperTypeComponent,\r\n    NgSelectTypeComponent,\r\n    FormlyHorizontalWrapper,\r\n    FormlyFieldTabsVertical,\r\n    FormlyFieldTabs,\r\n    CustomDateInputComponent,\r\n  ],\r\n  imports: [\r\n    FormlySelectModule,\r\n    NgSelectModule,\r\n    CommonModule,\r\n    FormsModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    ReactiveFormsModule,\r\n    CropperWithDialogModule,\r\n    NgbAlertModule,\r\n    NgbNavModule,\r\n    CKEditorModule,\r\n    FormlyModule.forRoot({\r\n      types: [\r\n        { name: 'datetime', component: DatatimepickerComponent },\r\n        { name: 'file', component: FormlyFieldFile, wrappers: ['form-field'] },\r\n        {\r\n          name: 'tabs-vertical',\r\n          component: FormlyFieldTabsVertical,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'ckeditor5',\r\n          component: Ckeditor5TypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'tabs',\r\n          component: FormlyFieldTabs,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'core-touchspin',\r\n          component: NumberTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'image-cropper',\r\n          component: ImageCropperTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'ng-select',\r\n          component: NgSelectTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        { name: 'details', component: DetailsWrapperComponent },\r\n        { name: 'repeat', component: RepeateFormTypeComponent },\r\n      ],\r\n      wrappers: [\r\n        { name: 'form-field-horizontal', component: FormlyHorizontalWrapper },\r\n      ],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n    FormlyBootstrapModule,\r\n    NgbDatepickerModule,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: FORMLY_CONFIG,\r\n      multi: true,\r\n      useFactory: registerTranslateExtension,\r\n      deps: [TranslateService],\r\n    },\r\n    {\r\n      provide: NgbDateParserFormatter,\r\n      useClass: CustomDateParserFormatter,\r\n    },\r\n  ],\r\n  exports: [\r\n    EditorSidebarComponent,\r\n    FormlyModule,\r\n    FormlyBootstrapModule,\r\n    ReactiveFormsModule,\r\n    CropperWithDialogModule,\r\n    NgbAlertModule,\r\n    CKEditorModule,\r\n  ],\r\n})\r\nexport class EditorSidebarModule {\r\n  constructor(_translateService: TranslateService, config: FormlyConfig) {\r\n    config.addValidatorMessage('max', (error, field) => {\r\n      return (\r\n        _translateService.instant('Value must be less than') +\r\n        ' ' +\r\n        (field.props.max + 1)\r\n      );\r\n    });\r\n    config.addValidatorMessage('min', (error, field) => {\r\n      return (\r\n        _translateService.instant('Value must be greater than') +\r\n        ' ' +\r\n        (field.props.min - 1)\r\n      );\r\n    });\r\n    config.addValidatorMessage('minlength', (error, field) => {\r\n      let fieldName = _translateService.instant(field.key as string);\r\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n      return (\r\n        _translateService.instant('This field must be at least', {\r\n          field: fieldName,\r\n        }) +\r\n        ' ' +\r\n        field.props.minLength +\r\n        ' ' +\r\n        _translateService.instant('characters')\r\n      );\r\n    });\r\n    config.addValidatorMessage('maxlength', (error, field) => {\r\n      let fieldName = _translateService.instant(field.key as string);\r\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n      return (\r\n        _translateService.instant('This field must be less than', {\r\n          field: fieldName,\r\n        }) +\r\n        ' ' +\r\n        field.props.maxLength +\r\n        ' ' +\r\n        _translateService.instant('characters')\r\n      );\r\n    });\r\n    config.addValidatorMessage('pattern', (error, field) => {\r\n      let fieldName = _translateService.instant(field.key as string);\r\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n      return _translateService.instant('This field is invalid', {\r\n        field: fieldName,\r\n      });\r\n    });\r\n    config.addValidatorMessage('required', (error, field) => {\r\n      let fieldName = _translateService.instant(field.key as string);\r\n      fieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n      // replace '_' with ' '\r\n      fieldName = fieldName.replace(/_/g, ' ');\r\n      return _translateService.instant('This field is required', {\r\n        field: fieldName,\r\n      });\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}