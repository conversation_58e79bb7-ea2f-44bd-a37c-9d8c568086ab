{"ast": null, "code": "import { FieldType } from \"@ngx-formly/bootstrap/form-field\";\nimport moment from \"moment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-formly/core\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nfunction DatatimepickerComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"formly-validation-message\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"field\", ctx_r1.field);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nexport class DatatimepickerComponent extends FieldType {\n  constructor(ngForm) {\n    super();\n    this.ngForm = ngForm;\n    this.submitted = false;\n    ngForm.ngSubmit.subscribe(event => {\n      this.submitted = true;\n    });\n  }\n  ngOnInit() {\n    // subscribe to value of formControl to update datetime\n    this.formControl.valueChanges.subscribe(value => {\n      let date = new Date(value);\n      this.dtPicker = {\n        day: date.getDate(),\n        month: date.getMonth() + 1,\n        year: date.getFullYear()\n      };\n    });\n  }\n  onDateSelect(event) {\n    console.log(\"onDateSelect: \", event);\n    // format date to yyyy-mm-dd using moment\n    let date = new Date(event.year, event.month - 1, event.day);\n    this.formControl.setValue(moment(date).format(\"YYYY-MM-DD\"));\n  }\n  onChanges(event) {\n    console.log(\"dtPicker: \", this.dtPicker);\n    if (this.dtPicker && this.dtPicker.hasOwnProperty('day')) {\n      let date = new Date(this.dtPicker.year, this.dtPicker.month - 1, this.dtPicker.day);\n      this.formControl.setValue(moment(date).format(\"YYYY-MM-DD\"));\n    }\n  }\n  onBlur($event) {\n    this.formControl.markAsTouched();\n  }\n  static #_ = this.ɵfac = function DatatimepickerComponent_Factory(t) {\n    return new (t || DatatimepickerComponent)(i0.ɵɵdirectiveInject(i1.FormGroupDirective));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DatatimepickerComponent,\n    selectors: [[\"app-datatimepicker\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 9,\n    vars: 10,\n    consts: [[1, \"form-group\"], [\"for\", \"datepicker\"], [1, \"input-group\"], [\"id\", \"datepicker\", \"placeholder\", \"dd/mm/yyyy\", \"ngbDatepicker\", \"\", 1, \"form-control\", 3, \"ngModel\", \"formlyAttributes\", \"disabled\", \"maxDate\", \"minDate\", \"ngClass\", \"ngModelChange\", \"dateSelect\", \"change\", \"blur\"], [\"datetime\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-outline-secondary\", \"feather\", \"icon-calendar\", 3, \"click\"], [\"class\", \"invalid-feedback ng-star-inserted\", \"style\", \"display: block\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"ng-star-inserted\", 2, \"display\", \"block\"], [3, \"field\"]],\n    template: function DatatimepickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r2 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"label\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 2)(4, \"input\", 3, 4);\n        i0.ɵɵlistener(\"ngModelChange\", function DatatimepickerComponent_Template_input_ngModelChange_4_listener($event) {\n          return ctx.dtPicker = $event;\n        })(\"dateSelect\", function DatatimepickerComponent_Template_input_dateSelect_4_listener($event) {\n          return ctx.onDateSelect($event);\n        })(\"change\", function DatatimepickerComponent_Template_input_change_4_listener($event) {\n          return ctx.onChanges($event);\n        })(\"blur\", function DatatimepickerComponent_Template_input_blur_4_listener($event) {\n          return ctx.onBlur($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function DatatimepickerComponent_Template_button_click_7_listener() {\n          i0.ɵɵrestoreView(_r2);\n          const _r0 = i0.ɵɵreference(5);\n          return i0.ɵɵresetView(_r0.toggle());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(8, DatatimepickerComponent_div_8_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.props.label ? ctx.props.label : \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.dtPicker)(\"formlyAttributes\", ctx.field)(\"disabled\", ctx.props.disabled ? ctx.props.disabled : false)(\"maxDate\", ctx.props.maxDate ? ctx.props.maxDate : undefined)(\"minDate\", ctx.props.minDate ? ctx.props.minDate : undefined)(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx.formControl.touched && ctx.formControl.errors || ctx.submitted));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.formControl.touched && ctx.formControl.errors || ctx.submitted);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgModel, i3.ɵFormlyAttributes, i3.ɵFormlyValidationMessage, i4.NgbInputDatepicker],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,SAAS,QAAQ,kCAAkC;AAE5D,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;IC0BzBC,8BAIC;IACCA,+CAAuE;IACzEA,iBAAM;;;;IADuBA,eAAe;IAAfA,oCAAe;;;;;;;;ADzB9C,OAAM,MAAOC,uBACX,SAAQH,SAA0B;EAKlCI,YAAoBC,MAA0B;IAC5C,KAAK,EAAE;IADW,WAAM,GAANA,MAAM;IADnB,cAAS,GAAG,KAAK;IAGtBA,MAAM,CAACC,QAAQ,CAACC,SAAS,CAACC,KAAK,IAAG;MAChC,IAAI,CAACC,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,WAAW,CAACC,YAAY,CAACL,SAAS,CAAEM,KAAK,IAAI;MAChD,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;MAC1B,IAAI,CAACG,QAAQ,GAAG;QACdC,GAAG,EAAEH,IAAI,CAACI,OAAO,EAAE;QACnBC,KAAK,EAAEL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC;QAC1BC,IAAI,EAAEP,IAAI,CAACQ,WAAW;OACvB;IACH,CAAC,CAAC;EACJ;EACAC,YAAY,CAACf,KAAU;IACrBgB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEjB,KAAK,CAAC;IACpC;IACA,IAAIM,IAAI,GAAG,IAAIC,IAAI,CAACP,KAAK,CAACa,IAAI,EAAEb,KAAK,CAACW,KAAK,GAAG,CAAC,EAAEX,KAAK,CAACS,GAAG,CAAC;IAC3D,IAAI,CAACN,WAAW,CAACe,QAAQ,CAACzB,MAAM,CAACa,IAAI,CAAC,CAACa,MAAM,CAAC,YAAY,CAAC,CAAC;EAC9D;EACAC,SAAS,CAACpB,KAAK;IACbgB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACT,QAAQ,CAAC;IACxC,IAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACa,cAAc,CAAC,KAAK,CAAC,EAAE;MACvD,IAAIf,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACC,QAAQ,CAACK,IAAI,EAAE,IAAI,CAACL,QAAQ,CAACG,KAAK,GAAG,CAAC,EAAE,IAAI,CAACH,QAAQ,CAACC,GAAG,CAAC;MACnF,IAAI,CAACN,WAAW,CAACe,QAAQ,CAACzB,MAAM,CAACa,IAAI,CAAC,CAACa,MAAM,CAAC,YAAY,CAAC,CAAC;;EAEhE;EACAG,MAAM,CAACC,MAAM;IACX,IAAI,CAACpB,WAAW,CAACqB,aAAa,EAAE;EAClC;EAAC;qBAvCU7B,uBAAuB;EAAA;EAAA;UAAvBA,uBAAuB;IAAA8B;IAAAC;IAAAC;IAAAC;IAAAC;IAAAC;MAAA;;QCXpCpC,8BAAwB;QACEA,YAAoC;QAAAA,iBAAQ;QACpEA,8BAAyB;QAGrBA;UAAA;QAAA,EAAsB;UAAA,OAORqC,wBAAoB;QAAA,EAPZ;UAAA,OAUZA,qBAAiB;QAAA,EAVL;UAAA,OAcdA,kBAAc;QAAA,EAdA;QAFxBrC,iBAiBE;QACFA,8BAAgC;QAG5BA;UAAAA;UAAA;UAAA,OAASA,2BAAiB;QAAA,EAAC;QAG5BA,iBAAS;QAIdA,wEAMM;QACRA,iBAAM;;;QArCoBA,eAAoC;QAApCA,4DAAoC;QAIxDA,eAAsB;QAAtBA,sCAAsB;QA6BvBA,eAA8D;QAA9DA,yFAA8D", "names": ["FieldType", "moment", "i0", "DatatimepickerComponent", "constructor", "ngForm", "ngSubmit", "subscribe", "event", "submitted", "ngOnInit", "formControl", "valueChanges", "value", "date", "Date", "dtPicker", "day", "getDate", "month", "getMonth", "year", "getFullYear", "onDateSelect", "console", "log", "setValue", "format", "onChanges", "hasOwnProperty", "onBlur", "$event", "<PERSON><PERSON><PERSON><PERSON>ched", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\layout\\components\\datatimepicker\\datatimepicker.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\layout\\components\\datatimepicker\\datatimepicker.component.html"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { FormGroupDirective } from \"@angular/forms\";\r\nimport { NgbDateStruct } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { FieldType } from \"@ngx-formly/bootstrap/form-field\";\r\nimport { FieldTypeConfig } from \"@ngx-formly/core\";\r\nimport moment from \"moment\";\r\n@Component({\r\n  selector: \"app-datatimepicker\",\r\n  templateUrl: \"./datatimepicker.component.html\",\r\n  styleUrls: [\"./datatimepicker.component.scss\"],\r\n})\r\nexport class DatatimepickerComponent\r\n  extends FieldType<FieldTypeConfig>\r\n  implements OnInit\r\n{\r\n  public dtPicker: NgbDateStruct; \r\n  public submitted = false;\r\n  constructor(private ngForm: FormGroupDirective) {\r\n    super();\r\n    ngForm.ngSubmit.subscribe(event => {\r\n      this.submitted = true;\r\n    });\r\n  }\r\n \r\n  ngOnInit(): void {\r\n    // subscribe to value of formControl to update datetime\r\n    this.formControl.valueChanges.subscribe((value) => {\r\n      let date = new Date(value);\r\n      this.dtPicker = {\r\n        day: date.getDate(),\r\n        month: date.getMonth() + 1,\r\n        year: date.getFullYear(),\r\n      };\r\n    });\r\n  }\r\n  onDateSelect(event: any) {\r\n    console.log(\"onDateSelect: \", event);\r\n    // format date to yyyy-mm-dd using moment\r\n    let date = new Date(event.year, event.month - 1, event.day);\r\n    this.formControl.setValue(moment(date).format(\"YYYY-MM-DD\"));\r\n  }\r\n  onChanges(event) {  \r\n    console.log(\"dtPicker: \", this.dtPicker);    \r\n    if(this.dtPicker && this.dtPicker.hasOwnProperty('day')) {\r\n      let date = new Date(this.dtPicker.year, this.dtPicker.month - 1, this.dtPicker.day);\r\n      this.formControl.setValue(moment(date).format(\"YYYY-MM-DD\"));\r\n    }\r\n  }\r\n  onBlur($event) {\r\n    this.formControl.markAsTouched();\r\n  }\r\n}\r\n", "<div class=\"form-group\">\r\n  <label for=\"datepicker\">{{ props.label ? props.label : '' }}</label>\r\n  <div class=\"input-group\">\r\n    <input\r\n      id=\"datepicker\"\r\n      [(ngModel)]=\"dtPicker\"\r\n      [formlyAttributes]=\"field\"\r\n      class=\"form-control\"\r\n      [disabled]=\"props.disabled ? props.disabled : false\"\r\n      placeholder=\"dd/mm/yyyy\"\r\n      ngbDatepicker\r\n      #datetime=\"ngbDatepicker\"\r\n      (dateSelect)=\"onDateSelect($event)\"\r\n      [maxDate]=\"props.maxDate ? props.maxDate : undefined\"\r\n      [minDate]=\"props.minDate ? props.minDate : undefined\"\r\n      (change)=\"onChanges($event)\"\r\n      [ngClass]=\"{\r\n        'is-invalid': (formControl.touched && formControl.errors) || submitted\r\n      }\"\r\n      (blur)=\"onBlur($event)\"\r\n    />\r\n    <div class=\"input-group-append\">\r\n      <button\r\n        class=\"btn btn-outline-secondary feather icon-calendar\"\r\n        (click)=\"datetime.toggle()\"\r\n        type=\"button\"\r\n        rippleEffect\r\n      ></button>\r\n    </div>\r\n  </div>\r\n\r\n  <div\r\n    class=\"invalid-feedback ng-star-inserted\"\r\n    style=\"display: block\"\r\n    *ngIf=\"(formControl.touched && formControl.errors) || submitted\"\r\n  >\r\n    <formly-validation-message [field]=\"field\"></formly-validation-message>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}