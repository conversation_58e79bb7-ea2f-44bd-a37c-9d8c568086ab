import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { AutoScheduleService } from '../../../../services/auto-schedule.service';
import { LoadingService } from 'app/services/loading.service';
import { ToastrService } from 'ngx-toastr';

export type UpdateConfigParams = {
    tournamentId: string;
    location: string;
    date: string;
    configId: number;
    timeSlotIds: number[];
}

@Component({
    selector: 'app-modal-update-config',
    templateUrl: './modal-update-config.component.html',
    styleUrls: ['./modal-update-config.component.scss']
})
export class ModalUpdateConfigComponent implements OnInit {

    @Input() selectedConfig: UpdateConfigParams | null = null;

    editForm = new FormGroup({});
    editModel = {};

    public editFields: FormlyFieldConfig[] = [
        {
            key: 'begin_date',
            type: 'custom-date',
            props: {
                label: this._translateService.instant('Date'),
                placeholder: this._translateService.instant('dd/mm/yyyy'),
                required: true
            }
        },
        {
            key: 'begin_time',
            type: 'input',
            props: {
                label: this._translateService.instant('Begin time'),
                placeholder: this._translateService.instant(
                    'Enter begin time'
                ),
                required: true,
                type: 'time'
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Begin time is required.')
                }
            }
        },
        {
            key: 'old_begin_time',
            type: 'input',
            props: {
                type: 'hidden'
            },
        },
        {
            key: 'match_duration',
            type: 'input',
            props: {
                label: this._translateService.instant('Match duration'),
                placeholder: this._translateService.instant(
                    'Enter match duration (in minutes)'
                ),
                required: true,
                type: 'number',
                min: 1,
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Match duration is required.'),
                    min: this._translateService.instant('Match duration must be at least 1 minute.')
                }
            }
        },
        {
            key: 'break_duration',
            type: 'input',
            props: {
                label: this._translateService.instant('Break duration'),
                placeholder: this._translateService.instant(
                    'Enter break between match duration (in minutes)'
                ),
                required: true,
                type: 'number',
                min: 0,
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Break duration is required.'),
                    min: this._translateService.instant('Break duration must be at least 0 minutes.')
                }
            }
        },
        {
            key: 'location_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'tournament_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'date',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'config_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
    ];

    @Output() onSubmit = new EventEmitter();

    constructor(
        private _modalService: NgbModal,
        private _translateService: TranslateService,
        private _autoSchedule: AutoScheduleService,
        private _loadingService: LoadingService,
        private _toastService: ToastrService
    ) {

    }

    ngOnInit() {
        if (!this.selectedConfig) {
            console.error('No selected configuration provided');
            return;
        }


        this._loadingService.show();

        this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId)
            .subscribe((res) => {
                console.log('🚀 ~ ngOnInit ~ res: ', res);
                // Convert ISO date string to 'YYYY-MM-DD' for input[type="date"]
                const beginDate = res.data.begin_date
                    ? new Date(res.data.begin_date).toISOString().slice(0, 10)
                    : '';

                this.editModel = {
                    ...this.editModel,
                    begin_date: beginDate,
                    begin_time: res.data.begin_time,
                    old_begin_time: `${beginDate} ${res.data.begin_time}`,
                    match_duration: res.data.match_duration,
                    break_duration: res.data.break_match_duration,
                    tournament_id: res.data.tournament_id,
                    location_id: res.data.location_id,
                    date: this.selectedConfig.date,
                    config_id: res.data.id,
                    time_slot_ids: this.selectedConfig.timeSlotIds,
                };

                this._loadingService.dismiss();

            });
    }

    onSubmitEdit(model) {
        console.log('🚀 ~ onSubmitEdit ~ model: ', model);
        this._autoSchedule.updateScheduleConfig(model).subscribe((res) => {
            this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));
            this.onSubmit.emit(res);
            this._modalService.dismissAll();
        }, (error) => {
            console.error('Error scheduling tournament:', error);
        });
    }


    closeModal() {
        this.editModel = {};
        this._modalService.dismissAll();
    }

    clearForm() {
        this.editForm.reset();
    }

}
