import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'localizedDate',
  pure: false,
})
export class LocalizedDatePipe implements PipeTransform {
  constructor(private translateService: TranslateService) { }

  transform(value: any, pattern: string = 'dd/MM/yyyy'): any {
    let locale = this.translateService.currentLang;
    switch (this.translateService.currentLang) {
      case 'zh_HK':
        locale = 'zh-Hant-HK';
        break;
      case 'zh_CN':
        locale = 'zh-Hans';
        break;
      case 'en_US':
        // Use en-GB locale for dd/mm/yyyy format instead of en-US (mm/dd/yyyy)
        locale = 'en-GB';
        break;
      case 'en':
        // Use en-GB locale for dd/mm/yyyy format
        locale = 'en-GB';
        break;
      default:
        // Default to en-GB for dd/mm/yyyy format
        locale = 'en-GB';
        break;
    }

    const datePipe: DatePipe = new DatePipe(locale);
    return datePipe.transform(value, pattern);
  }
}
